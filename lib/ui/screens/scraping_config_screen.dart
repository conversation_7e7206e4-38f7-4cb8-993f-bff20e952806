import 'package:flutter/material.dart';
import 'package:aso_scraper_web/scraper/constants.dart';
import 'package:aso_scraper_web/scraper/countries.dart';
import 'package:aso_scraper_web/services/auth_service.dart';
import 'package:aso_scraper_web/ui/widgets/country_multi_select.dart';
import 'package:aso_scraper_web/ui/widgets/config_slider.dart';

class ScrapingConfigScreen extends StatefulWidget {
  const ScrapingConfigScreen({super.key});

  @override
  State<ScrapingConfigScreen> createState() => _ScrapingConfigScreenState();
}

class _ScrapingConfigScreenState extends State<ScrapingConfigScreen> {
  final _formKey = GlobalKey<FormState>();
  final _appDescriptionController = TextEditingController();
  final _supportUrlController = TextEditingController();
  final _marketingUrlController = TextEditingController();
  final _privacyPolicyUrlController = TextEditingController();
  final _termsOfServiceUrlController = TextEditingController();

  // Slider values
  double _topAppsCount = 1;
  double _minInstall = 1;
  double _maxDifficulty = 70;
  double _minRelation = 1;

  // Selected countries
  List<CountryModel> _selectedCountries = List.from(countryModels);

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
  }


  @override
  void dispose() {
    _appDescriptionController.dispose();
    _supportUrlController.dispose();
    _marketingUrlController.dispose();
    _privacyPolicyUrlController.dispose();
    _termsOfServiceUrlController.dispose();
    super.dispose();
  }

  Future<void> _handleLogout() async {
    final authService = AuthService();
    await authService.clearCredentials();
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/auth');
    }
  }

  Future<void> _startScraping() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCountries.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one country'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await Future.delayed(const Duration(seconds: 2));
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Scraping started successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start scraping: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scraping Configuration'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _handleLogout,
            tooltip: 'Logout',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 800),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Scraping Parameters Section
                _buildSectionCard(
                  title: 'Scraping Parameters',
                  icon: Icons.tune,
                  children: [
                    ConfigSlider(
                      label: 'Top Apps Count',
                      value: _topAppsCount,
                      min: 1,
                      max: 20,
                      divisions: 19,
                      onChanged: (value) => setState(() => _topAppsCount = value),
                    ),
                    const SizedBox(height: 16),
                    ConfigSlider(
                      label: 'Minimum Install',
                      value: _minInstall,
                      min: 1,
                      max: 100,
                      divisions: 99,
                      onChanged: (value) => setState(() => _minInstall = value),
                    ),
                    const SizedBox(height: 16),
                    ConfigSlider(
                      label: 'Maximum Difficulty',
                      value: _maxDifficulty,
                      min: 1,
                      max: 100,
                      divisions: 99,
                      onChanged: (value) => setState(() => _maxDifficulty = value),
                    ),
                    const SizedBox(height: 16),
                    ConfigSlider(
                      label: 'Minimum Relation',
                      value: _minRelation,
                      min: 1,
                      max: 100,
                      divisions: 99,
                      onChanged: (value) => setState(() => _minRelation = value),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // App Information Section
                _buildSectionCard(
                  title: 'App Information',
                  icon: Icons.info_outline,
                  children: [
                    TextFormField(
                      controller: _appDescriptionController,
                      decoration: const InputDecoration(
                        labelText: 'App Description *',
                        hintText: 'Enter your app description',
                      ),
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'App description is required';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _supportUrlController,
                      decoration: const InputDecoration(
                        labelText: 'App Support URL',
                        hintText: 'https://support.example.com',
                      ),
                      validator: _urlValidator,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _marketingUrlController,
                      decoration: const InputDecoration(
                        labelText: 'App Marketing URL',
                        hintText: 'https://marketing.example.com',
                      ),
                      validator: _urlValidator,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _privacyPolicyUrlController,
                      decoration: const InputDecoration(
                        labelText: 'App Privacy Policy URL',
                        hintText: 'https://privacy.example.com',
                      ),
                      validator: _urlValidator,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _termsOfServiceUrlController,
                      decoration: const InputDecoration(
                        labelText: 'App Terms of Service URL',
                        hintText: 'https://terms.example.com',
                      ),
                      validator: _urlValidator,
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Target Countries Section
                _buildSectionCard(
                  title: 'Target Countries',
                  icon: Icons.public,
                  children: [
                    CountryMultiSelect(
                      countries: countryModels,
                      selectedCountries: _selectedCountries,
                      onSelectionChanged: (selected) {
                        setState(() {
                          _selectedCountries = selected;
                        });
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // Start Scraping Button
                ElevatedButton(
                  onPressed: _isLoading ? null : _startScraping,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Start Scraping',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  String? _urlValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Optional field
    }
    
    final urlPattern = RegExp(
      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
    );
    
    if (!urlPattern.hasMatch(value.trim())) {
      return 'Please enter a valid URL';
    }
    
    return null;
  }
}
