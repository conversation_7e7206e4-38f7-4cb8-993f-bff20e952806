import 'package:flutter/material.dart';
import 'package:aso_scraper_web/scraper/countries.dart';

class CountryMultiSelect extends StatefulWidget {
  final List<CountryModel> countries;
  final List<CountryModel> selectedCountries;
  final ValueChanged<List<CountryModel>> onSelectionChanged;

  const CountryMultiSelect({
    super.key,
    required this.countries,
    required this.selectedCountries,
    required this.onSelectionChanged,
  });

  @override
  State<CountryMultiSelect> createState() => _CountryMultiSelectState();
}

class _CountryMultiSelectState extends State<CountryMultiSelect> {
  bool get _isAllSelected => widget.selectedCountries.length == widget.countries.length;

  void _toggleSelectAll() {
    if (_isAllSelected) {
      widget.onSelectionChanged([]);
    } else {
      widget.onSelectionChanged(List.from(widget.countries));
    }
  }

  void _toggleCountry(CountryModel country) {
    final newSelection = List<CountryModel>.from(widget.selectedCountries);
    if (newSelection.contains(country)) {
      newSelection.remove(country);
    } else {
      newSelection.add(country);
    }
    widget.onSelectionChanged(newSelection);
  }

  String _getCountryName(String countryCode) {
    // Map of country codes to names
    const countryNames = {
      'SA': 'Saudi Arabia',
      'ES': 'Spain',
      'CN': 'China',
      'TW': 'Taiwan',
      'HR': 'Croatia',
      'CZ': 'Czech Republic',
      'DK': 'Denmark',
      'NL': 'Netherlands',
      'FI': 'Finland',
      'FR': 'France',
      'DE': 'Germany',
      'GR': 'Greece',
      'HU': 'Hungary',
      'IS': 'Iceland',
      'IE': 'Ireland',
      'IT': 'Italy',
      'LV': 'Latvia',
      'LT': 'Lithuania',
      'LU': 'Luxembourg',
      'MT': 'Malta',
      'NO': 'Norway',
      'PL': 'Poland',
      'PT': 'Portugal',
      'RO': 'Romania',
      'SK': 'Slovakia',
      'SI': 'Slovenia',
      'SE': 'Sweden',
      'CH': 'Switzerland',
      'GB': 'United Kingdom',
      'US': 'United States',
      'AU': 'Australia',
      'CA': 'Canada',
      'JP': 'Japan',
      'KR': 'South Korea',
      'IN': 'India',
      'BR': 'Brazil',
      'MX': 'Mexico',
      'AR': 'Argentina',
      'CL': 'Chile',
      'CO': 'Colombia',
      'PE': 'Peru',
      'VE': 'Venezuela',
      'ZA': 'South Africa',
      'EG': 'Egypt',
      'NG': 'Nigeria',
      'KE': 'Kenya',
      'MA': 'Morocco',
      'TN': 'Tunisia',
      'GH': 'Ghana',
      'UG': 'Uganda',
      'TZ': 'Tanzania',
      'ZW': 'Zimbabwe',
      'BW': 'Botswana',
      'ZM': 'Zambia',
      'MW': 'Malawi',
      'MZ': 'Mozambique',
      'AO': 'Angola',
      'NA': 'Namibia',
      'SZ': 'Eswatini',
      'LS': 'Lesotho',
    };
    
    return countryNames[countryCode] ?? countryCode;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Select All / Deselect All Button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Selected: ${widget.selectedCountries.length} of ${widget.countries.length}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            TextButton.icon(
              onPressed: _toggleSelectAll,
              icon: Icon(_isAllSelected ? Icons.deselect : Icons.select_all),
              label: Text(_isAllSelected ? 'Deselect All' : 'Select All'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // Countries Grid
        Container(
          constraints: const BoxConstraints(maxHeight: 300),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Scrollbar(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: widget.countries.length,
              itemBuilder: (context, index) {
                final country = widget.countries[index];
                final isSelected = widget.selectedCountries.contains(country);
                
                return CheckboxListTile(
                  value: isSelected,
                  onChanged: (_) => _toggleCountry(country),
                  title: Text(_getCountryName(country.countryCode)),
                  subtitle: Text(
                    '${country.countryCode} • ${country.languageCode}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  dense: true,
                  controlAffinity: ListTileControlAffinity.leading,
                  activeColor: Theme.of(context).colorScheme.primary,
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
