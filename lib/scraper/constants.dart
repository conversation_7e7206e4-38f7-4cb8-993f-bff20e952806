import 'package:aso_scraper_web/scraper/countries.dart';

int kAppNameCol = 4;
int kSubTitleCol = 7;
int kKeywordsCol = 10;
int kDescriptionCol = 13;
int kSupportUrlCol = 15;
int kMarketingUrlCol = 16;
int kPromotinalTextCol = 17;

// List of CountryModel with excelRow incremented by 1
List<CountryModel> countryModels = [
  CountryModel(
    countryCode: 'SA',
    excelRow: 1,
    languageCode: 'ar-SA',
  ), // Saudi Arabia - Arabic
  CountryModel(
    countryCode: 'ES',
    excelRow: 2,
    languageCode: 'es-ES',
  ), // Spain - Spanish
  CountryModel(
    countryCode: 'CN',
    excelRow: 3,
    languageCode: 'zh-CN',
  ), // China - Chinese (Mandarin)
  CountryModel(
    countryCode: 'TW',
    excelRow: 4,
    languageCode: 'zh-TW',
  ), // Taiwan - Chinese (Mandarin)
  CountryModel(
    countryCode: 'HR',
    excelRow: 5,
    languageCode: 'hr-HR',
  ), // Croatia - Croatian
  CountryModel(
    countryCode: 'CZ',
    excelRow: 6,
    languageCode: 'cs-CZ',
  ), // Czech Republic - Czech
  CountryModel(
    countryCode: 'DK',
    excelRow: 7,
    languageCode: 'da-DK',
  ), // Denmark - Danish
  CountryModel(
    countryCode: 'NL',
    excelRow: 8,
    languageCode: 'nl-NL',
  ), // Netherlands - Dutch
  CountryModel(
    countryCode: 'AU',
    excelRow: 9,
    languageCode: 'en-AU',
  ), // Australia - English
  CountryModel(
    countryCode: 'CA',
    excelRow: 10,
    languageCode: 'en-CA',
  ), // Canada - English
  CountryModel(
    countryCode: 'GB',
    excelRow: 11,
    languageCode: 'en-GB',
  ), // United Kingdom - English
  CountryModel(
    countryCode: 'US',
    excelRow: 12,
    languageCode: 'en-US',
  ), // United States - English
  CountryModel(
    countryCode: 'FI',
    excelRow: 13,
    languageCode: 'fi-FI',
  ), // Finland - Finnish
  CountryModel(
    countryCode: 'FR',
    excelRow: 14,
    languageCode: 'fr-FR',
  ), // France - French
  CountryModel(
    countryCode: 'CA',
    excelRow: 15,
    languageCode: 'en-CA',
  ), // Canada - English
  CountryModel(
    countryCode: 'DE',
    excelRow: 16,
    languageCode: 'de-DE',
  ), // Germany - German
  CountryModel(
    countryCode: 'GR',
    excelRow: 17,
    languageCode: 'el-GR',
  ), // Greece - Greek
  CountryModel(
    countryCode: 'IL',
    excelRow: 18,
    languageCode: 'he-IL',
  ), // Israel - Hebrew
  CountryModel(
    countryCode: 'IN',
    excelRow: 19,
    languageCode: 'hi-IN',
  ), // India - Hindi
  CountryModel(
    countryCode: 'HU',
    excelRow: 20,
    languageCode: 'hu-HU',
  ), // Hungary - Hungarian
  CountryModel(
    countryCode: 'ID',
    excelRow: 21,
    languageCode: 'id-ID',
  ), // Indonesia - Indonesian
  CountryModel(
    countryCode: 'IT',
    excelRow: 22,
    languageCode: 'it-IT',
  ), // Italy - Italian
  CountryModel(
    countryCode: 'JP',
    excelRow: 23,
    languageCode: 'ja-JP',
  ), // Japan - Japanese
  CountryModel(
    countryCode: 'KR',
    excelRow: 24,
    languageCode: 'ko-KR',
  ), // South Korea - Korean
  CountryModel(
    countryCode: 'MY',
    excelRow: 25,
    languageCode: 'ms-MY',
  ), // Malaysia - Malay
  CountryModel(
    countryCode: 'NO',
    excelRow: 26,
    languageCode: 'no-NO',
  ), // Norway - Norwegian
  CountryModel(
    countryCode: 'PL',
    excelRow: 27,
    languageCode: 'pl-PL',
  ), // Poland - Polish
  CountryModel(
    countryCode: 'BR',
    excelRow: 28,
    languageCode: 'pt-BR',
  ), // Brazil - Portuguese
  CountryModel(
    countryCode: 'PT',
    excelRow: 29,
    languageCode: 'pt-PT',
  ), // Portugal - Portuguese
  CountryModel(
    countryCode: 'RO',
    excelRow: 30,
    languageCode: 'ro-RO',
  ), // Romania - Romanian
  CountryModel(
    countryCode: 'RU',
    excelRow: 31,
    languageCode: 'ru-RU',
  ), // Russia - Russian
  CountryModel(
    countryCode: 'SK',
    excelRow: 32,
    languageCode: 'sk-SK',
  ), // Slovakia - Slovak
  CountryModel(
    countryCode: 'MX',
    excelRow: 33,
    languageCode: 'es-MX',
  ), // Mexico - Spanish
  CountryModel(
    countryCode: 'ES',
    excelRow: 34,
    languageCode: 'es-ES',
  ), // Spain - Spanish
  CountryModel(
    countryCode: 'SE',
    excelRow: 35,
    languageCode: 'sv-SE',
  ), // Sweden - Swedish
  CountryModel(
    countryCode: 'TH',
    excelRow: 36,
    languageCode: 'th-TH',
  ), // Thailand - Thai
  CountryModel(
    countryCode: 'TR',
    excelRow: 37,
    languageCode: 'tr-TR',
  ), // Turkey - Turkish
  CountryModel(
    countryCode: 'UA',
    excelRow: 38,
    languageCode: 'uk-UA',
  ), // Ukraine - Ukrainian
  CountryModel(
    countryCode: 'VN',
    excelRow: 39,
    languageCode: 'vi-VN',
  ), // Vietnam - Vietnamese
];
