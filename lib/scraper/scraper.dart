import 'dart:async';
import 'dart:io';

import 'package:aso_scraper_web/scraper/client/fox_data_client.dart';
import 'package:aso_scraper_web/scraper/client/open_ai_client.dart';
import 'package:aso_scraper_web/scraper/config.dart';
import 'package:aso_scraper_web/scraper/constants.dart';
import 'package:aso_scraper_web/scraper/countries.dart';
import 'package:aso_scraper_web/scraper/models/app_keyword_response.dart';
import 'package:excel/excel.dart';

Future<List<ExcelValue>> scrap(CountryModel country) async {
  final openai = OpenAI();

  final client = FoxDataASOClient(
    email: foxDataEmail,
    password: foxDataPassword,
  );

  final excelValues = <ExcelValue>[];

  try {
    print('*****************GENERATING APP METADATA FOR COUNTRY: ${country.countryCode}*****************');


      // Search for apps
      final searchResponse = await client.getTopNAppByKeyword(
        keyword: baseKeyword,
        country: country.countryCode,
        pageSize: topAppsCount,
      );
      
      print('For ${country.countryCode} country, found ${searchResponse.data.list.length} apps.\n');
      for (var app in searchResponse.data.list) {
        print('App: ${app.appName}, Installs: ${app.install}');
      }

      List<KeywordItem> keywords = [];

      for (var app in searchResponse.data.list) {
        final appResponse = await client.getKeywordsByAppId(
          appId: app.appId,
          maxDifficulty: maxDifficulty,
          minRelation: minRelation,
          minInstall: minInstall,
          country: country.countryCode,
        );
        keywords.addAll(appResponse.data.list);
      }

    
      Map<String, KeywordItem> keywordMap = {};

      // keywords listesini tarayıp aynı keyword'lerin install değerlerini topluyoruz
      for (var keyword in keywords) {
        String key = keyword.keyword; // keyword değeri
        int install = keyword.install ?? 0; // install değeri, null ise 0
        int? difficultyNum = keyword.difficultyNum; // difficulty değeri

        keywordMap.update(
          key,
          (value) => KeywordItem(
            keyword: key,
            install: value.install! + install, // install değerini topla
            difficultyNum: value.difficultyNum, // difficulty aynı kalır
          ),
          ifAbsent: () => KeywordItem(
            keyword: key,
            install: install,
            difficultyNum: difficultyNum,
          ),
        );
      }

      // Sonuçları liste olarak oluşturuyoruz
      List<KeywordItem> uniqueKeywords = keywordMap.values.toList();

      final appTitle = await openai.generateTitle(keywords: uniqueKeywords, countryCode: country.countryCode);
      excelValues.add(ExcelValue(rowIndex: country.excelRow, columnIndex: kAppNameCol, title: 'App Name', value: appTitle));
      final appSubtitle = await openai.generateSubtitle(keywords: uniqueKeywords, countryCode: country.countryCode, appTitle: appTitle);
      excelValues.add(ExcelValue(rowIndex: country.excelRow, columnIndex: kSubTitleCol, title: 'App Subtitle', value: appSubtitle));
      final aiKeywords = await openai.generateKeywords(keywords: uniqueKeywords, countryCode: country.countryCode, appTitle: appTitle, appSubtitle: appSubtitle);
      excelValues.add(ExcelValue(rowIndex: country.excelRow, columnIndex: kKeywordsCol, title: 'Keywords', value: aiKeywords));
      final promotionalText = await openai.generatePromotionalText(countryCode: country.countryCode, appTitle: appTitle, appSubtitle: appSubtitle);
      excelValues.add(ExcelValue(rowIndex: country.excelRow, columnIndex: kPromotinalTextCol, title: 'Promotional Text', value: promotionalText));
      final appDescription = await openai.generateAppDescription( countryCode: country.countryCode, appTitle: appTitle, appSubtitle: appSubtitle);
      excelValues.add(ExcelValue(rowIndex: country.excelRow, columnIndex: kDescriptionCol, title: 'App Description', value: appDescription));
      excelValues.add(ExcelValue(rowIndex: country.excelRow, columnIndex: kSupportUrlCol, title: 'Support URL', value: appSupportUrl));
      excelValues.add(ExcelValue(rowIndex: country.excelRow, columnIndex: kMarketingUrlCol, title: 'Marketing URL', value: appMarketingUrl));

      excelValues.sort((a, b) => a.columnIndex.compareTo(b.columnIndex));

      return excelValues;
  } catch (e) {
    rethrow;
  }
}

Future<void> writeToExcel(List<ExcelValue> values) async {
  // Load the Excel file (adjust path as needed)
  var file = 'default.xlsx'; // If in assets, ensure proper loading
  var bytes = File(file).readAsBytesSync();
  var excel = Excel.decodeBytes(bytes);

  // Access the default sheet (adjust sheet name if different)
  var sheet = excel['Sheet1'];

  // Update the Keywords column for each language
  for (var value in values) {
    // Skip header row
    sheet
        .cell(
          CellIndex.indexByColumnRow(
            columnIndex: value.columnIndex,
            rowIndex: value.rowIndex,
          ),
        )
        .value = TextCellValue(
      value.value,
    );
    ;
  }

  excel.save();

  // Save the modified Excel file
  var outputFile = File('default_updated_${DateTime.now().millisecondsSinceEpoch}.xlsx');
  var updatedBytes = excel.encode();
  outputFile.writeAsBytesSync(updatedBytes!);
}





class ExcelValue {
  int rowIndex;
  int columnIndex;
  String title;
  String value;

  ExcelValue({required this.rowIndex, required this.columnIndex, required this.title, required this.value});
}