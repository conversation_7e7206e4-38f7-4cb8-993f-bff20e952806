// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'keyword_coverage_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$KeywordCoverageResponse {

 int get code; String? get msg; KeywordCoverageData? get data;
/// Create a copy of KeywordCoverageResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$KeywordCoverageResponseCopyWith<KeywordCoverageResponse> get copyWith => _$KeywordCoverageResponseCopyWithImpl<KeywordCoverageResponse>(this as KeywordCoverageResponse, _$identity);

  /// Serializes this KeywordCoverageResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is KeywordCoverageResponse&&(identical(other.code, code) || other.code == code)&&(identical(other.msg, msg) || other.msg == msg)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,msg,data);

@override
String toString() {
  return 'KeywordCoverageResponse(code: $code, msg: $msg, data: $data)';
}


}

/// @nodoc
abstract mixin class $KeywordCoverageResponseCopyWith<$Res>  {
  factory $KeywordCoverageResponseCopyWith(KeywordCoverageResponse value, $Res Function(KeywordCoverageResponse) _then) = _$KeywordCoverageResponseCopyWithImpl;
@useResult
$Res call({
 int code, String? msg, KeywordCoverageData? data
});


$KeywordCoverageDataCopyWith<$Res>? get data;

}
/// @nodoc
class _$KeywordCoverageResponseCopyWithImpl<$Res>
    implements $KeywordCoverageResponseCopyWith<$Res> {
  _$KeywordCoverageResponseCopyWithImpl(this._self, this._then);

  final KeywordCoverageResponse _self;
  final $Res Function(KeywordCoverageResponse) _then;

/// Create a copy of KeywordCoverageResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? code = null,Object? msg = freezed,Object? data = freezed,}) {
  return _then(_self.copyWith(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,msg: freezed == msg ? _self.msg : msg // ignore: cast_nullable_to_non_nullable
as String?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as KeywordCoverageData?,
  ));
}
/// Create a copy of KeywordCoverageResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$KeywordCoverageDataCopyWith<$Res>? get data {
    if (_self.data == null) {
    return null;
  }

  return $KeywordCoverageDataCopyWith<$Res>(_self.data!, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _KeywordCoverageResponse implements KeywordCoverageResponse {
  const _KeywordCoverageResponse({required this.code, this.msg, this.data});
  factory _KeywordCoverageResponse.fromJson(Map<String, dynamic> json) => _$KeywordCoverageResponseFromJson(json);

@override final  int code;
@override final  String? msg;
@override final  KeywordCoverageData? data;

/// Create a copy of KeywordCoverageResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$KeywordCoverageResponseCopyWith<_KeywordCoverageResponse> get copyWith => __$KeywordCoverageResponseCopyWithImpl<_KeywordCoverageResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$KeywordCoverageResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _KeywordCoverageResponse&&(identical(other.code, code) || other.code == code)&&(identical(other.msg, msg) || other.msg == msg)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,msg,data);

@override
String toString() {
  return 'KeywordCoverageResponse(code: $code, msg: $msg, data: $data)';
}


}

/// @nodoc
abstract mixin class _$KeywordCoverageResponseCopyWith<$Res> implements $KeywordCoverageResponseCopyWith<$Res> {
  factory _$KeywordCoverageResponseCopyWith(_KeywordCoverageResponse value, $Res Function(_KeywordCoverageResponse) _then) = __$KeywordCoverageResponseCopyWithImpl;
@override @useResult
$Res call({
 int code, String? msg, KeywordCoverageData? data
});


@override $KeywordCoverageDataCopyWith<$Res>? get data;

}
/// @nodoc
class __$KeywordCoverageResponseCopyWithImpl<$Res>
    implements _$KeywordCoverageResponseCopyWith<$Res> {
  __$KeywordCoverageResponseCopyWithImpl(this._self, this._then);

  final _KeywordCoverageResponse _self;
  final $Res Function(_KeywordCoverageResponse) _then;

/// Create a copy of KeywordCoverageResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? msg = freezed,Object? data = freezed,}) {
  return _then(_KeywordCoverageResponse(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,msg: freezed == msg ? _self.msg : msg // ignore: cast_nullable_to_non_nullable
as String?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as KeywordCoverageData?,
  ));
}

/// Create a copy of KeywordCoverageResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$KeywordCoverageDataCopyWith<$Res>? get data {
    if (_self.data == null) {
    return null;
  }

  return $KeywordCoverageDataCopyWith<$Res>(_self.data!, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
mixin _$KeywordCoverageData {

 List<KeywordData> get list;
/// Create a copy of KeywordCoverageData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$KeywordCoverageDataCopyWith<KeywordCoverageData> get copyWith => _$KeywordCoverageDataCopyWithImpl<KeywordCoverageData>(this as KeywordCoverageData, _$identity);

  /// Serializes this KeywordCoverageData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is KeywordCoverageData&&const DeepCollectionEquality().equals(other.list, list));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(list));

@override
String toString() {
  return 'KeywordCoverageData(list: $list)';
}


}

/// @nodoc
abstract mixin class $KeywordCoverageDataCopyWith<$Res>  {
  factory $KeywordCoverageDataCopyWith(KeywordCoverageData value, $Res Function(KeywordCoverageData) _then) = _$KeywordCoverageDataCopyWithImpl;
@useResult
$Res call({
 List<KeywordData> list
});




}
/// @nodoc
class _$KeywordCoverageDataCopyWithImpl<$Res>
    implements $KeywordCoverageDataCopyWith<$Res> {
  _$KeywordCoverageDataCopyWithImpl(this._self, this._then);

  final KeywordCoverageData _self;
  final $Res Function(KeywordCoverageData) _then;

/// Create a copy of KeywordCoverageData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? list = null,}) {
  return _then(_self.copyWith(
list: null == list ? _self.list : list // ignore: cast_nullable_to_non_nullable
as List<KeywordData>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _KeywordCoverageData implements KeywordCoverageData {
  const _KeywordCoverageData({final  List<KeywordData> list = const []}): _list = list;
  factory _KeywordCoverageData.fromJson(Map<String, dynamic> json) => _$KeywordCoverageDataFromJson(json);

 final  List<KeywordData> _list;
@override@JsonKey() List<KeywordData> get list {
  if (_list is EqualUnmodifiableListView) return _list;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_list);
}


/// Create a copy of KeywordCoverageData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$KeywordCoverageDataCopyWith<_KeywordCoverageData> get copyWith => __$KeywordCoverageDataCopyWithImpl<_KeywordCoverageData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$KeywordCoverageDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _KeywordCoverageData&&const DeepCollectionEquality().equals(other._list, _list));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_list));

@override
String toString() {
  return 'KeywordCoverageData(list: $list)';
}


}

/// @nodoc
abstract mixin class _$KeywordCoverageDataCopyWith<$Res> implements $KeywordCoverageDataCopyWith<$Res> {
  factory _$KeywordCoverageDataCopyWith(_KeywordCoverageData value, $Res Function(_KeywordCoverageData) _then) = __$KeywordCoverageDataCopyWithImpl;
@override @useResult
$Res call({
 List<KeywordData> list
});




}
/// @nodoc
class __$KeywordCoverageDataCopyWithImpl<$Res>
    implements _$KeywordCoverageDataCopyWith<$Res> {
  __$KeywordCoverageDataCopyWithImpl(this._self, this._then);

  final _KeywordCoverageData _self;
  final $Res Function(_KeywordCoverageData) _then;

/// Create a copy of KeywordCoverageData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? list = null,}) {
  return _then(_KeywordCoverageData(
list: null == list ? _self._list : list // ignore: cast_nullable_to_non_nullable
as List<KeywordData>,
  ));
}


}


/// @nodoc
mixin _$KeywordData {

 String? get keyword; int? get volume; int? get difficulty; int? get rank; int? get install;
/// Create a copy of KeywordData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$KeywordDataCopyWith<KeywordData> get copyWith => _$KeywordDataCopyWithImpl<KeywordData>(this as KeywordData, _$identity);

  /// Serializes this KeywordData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is KeywordData&&(identical(other.keyword, keyword) || other.keyword == keyword)&&(identical(other.volume, volume) || other.volume == volume)&&(identical(other.difficulty, difficulty) || other.difficulty == difficulty)&&(identical(other.rank, rank) || other.rank == rank)&&(identical(other.install, install) || other.install == install));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,keyword,volume,difficulty,rank,install);

@override
String toString() {
  return 'KeywordData(keyword: $keyword, volume: $volume, difficulty: $difficulty, rank: $rank, install: $install)';
}


}

/// @nodoc
abstract mixin class $KeywordDataCopyWith<$Res>  {
  factory $KeywordDataCopyWith(KeywordData value, $Res Function(KeywordData) _then) = _$KeywordDataCopyWithImpl;
@useResult
$Res call({
 String? keyword, int? volume, int? difficulty, int? rank, int? install
});




}
/// @nodoc
class _$KeywordDataCopyWithImpl<$Res>
    implements $KeywordDataCopyWith<$Res> {
  _$KeywordDataCopyWithImpl(this._self, this._then);

  final KeywordData _self;
  final $Res Function(KeywordData) _then;

/// Create a copy of KeywordData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? keyword = freezed,Object? volume = freezed,Object? difficulty = freezed,Object? rank = freezed,Object? install = freezed,}) {
  return _then(_self.copyWith(
keyword: freezed == keyword ? _self.keyword : keyword // ignore: cast_nullable_to_non_nullable
as String?,volume: freezed == volume ? _self.volume : volume // ignore: cast_nullable_to_non_nullable
as int?,difficulty: freezed == difficulty ? _self.difficulty : difficulty // ignore: cast_nullable_to_non_nullable
as int?,rank: freezed == rank ? _self.rank : rank // ignore: cast_nullable_to_non_nullable
as int?,install: freezed == install ? _self.install : install // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _KeywordData implements KeywordData {
  const _KeywordData({this.keyword, this.volume, this.difficulty, this.rank, this.install});
  factory _KeywordData.fromJson(Map<String, dynamic> json) => _$KeywordDataFromJson(json);

@override final  String? keyword;
@override final  int? volume;
@override final  int? difficulty;
@override final  int? rank;
@override final  int? install;

/// Create a copy of KeywordData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$KeywordDataCopyWith<_KeywordData> get copyWith => __$KeywordDataCopyWithImpl<_KeywordData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$KeywordDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _KeywordData&&(identical(other.keyword, keyword) || other.keyword == keyword)&&(identical(other.volume, volume) || other.volume == volume)&&(identical(other.difficulty, difficulty) || other.difficulty == difficulty)&&(identical(other.rank, rank) || other.rank == rank)&&(identical(other.install, install) || other.install == install));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,keyword,volume,difficulty,rank,install);

@override
String toString() {
  return 'KeywordData(keyword: $keyword, volume: $volume, difficulty: $difficulty, rank: $rank, install: $install)';
}


}

/// @nodoc
abstract mixin class _$KeywordDataCopyWith<$Res> implements $KeywordDataCopyWith<$Res> {
  factory _$KeywordDataCopyWith(_KeywordData value, $Res Function(_KeywordData) _then) = __$KeywordDataCopyWithImpl;
@override @useResult
$Res call({
 String? keyword, int? volume, int? difficulty, int? rank, int? install
});




}
/// @nodoc
class __$KeywordDataCopyWithImpl<$Res>
    implements _$KeywordDataCopyWith<$Res> {
  __$KeywordDataCopyWithImpl(this._self, this._then);

  final _KeywordData _self;
  final $Res Function(_KeywordData) _then;

/// Create a copy of KeywordData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? keyword = freezed,Object? volume = freezed,Object? difficulty = freezed,Object? rank = freezed,Object? install = freezed,}) {
  return _then(_KeywordData(
keyword: freezed == keyword ? _self.keyword : keyword // ignore: cast_nullable_to_non_nullable
as String?,volume: freezed == volume ? _self.volume : volume // ignore: cast_nullable_to_non_nullable
as int?,difficulty: freezed == difficulty ? _self.difficulty : difficulty // ignore: cast_nullable_to_non_nullable
as int?,rank: freezed == rank ? _self.rank : rank // ignore: cast_nullable_to_non_nullable
as int?,install: freezed == install ? _self.install : install // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
