// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'keyword_coverage_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_KeywordCoverageResponse _$KeywordCoverageResponseFromJson(
  Map<String, dynamic> json,
) => _KeywordCoverageResponse(
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String?,
  data: json['data'] == null
      ? null
      : KeywordCoverageData.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$KeywordCoverageResponseToJson(
  _KeywordCoverageResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'data': instance.data,
};

_KeywordCoverageData _$KeywordCoverageDataFromJson(Map<String, dynamic> json) =>
    _KeywordCoverageData(
      list:
          (json['list'] as List<dynamic>?)
              ?.map((e) => KeywordData.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$KeywordCoverageDataToJson(
  _KeywordCoverageData instance,
) => <String, dynamic>{'list': instance.list};

_KeywordData _$KeywordDataFromJson(Map<String, dynamic> json) => _KeywordData(
  keyword: json['keyword'] as String?,
  volume: (json['volume'] as num?)?.toInt(),
  difficulty: (json['difficulty'] as num?)?.toInt(),
  rank: (json['rank'] as num?)?.toInt(),
  install: (json['install'] as num?)?.toInt(),
);

Map<String, dynamic> _$KeywordDataToJson(_KeywordData instance) =>
    <String, dynamic>{
      'keyword': instance.keyword,
      'volume': instance.volume,
      'difficulty': instance.difficulty,
      'rank': instance.rank,
      'install': instance.install,
    };
