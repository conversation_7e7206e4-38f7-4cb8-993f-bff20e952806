// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_keyword_apps_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SearchKeywordAppsResponse _$SearchKeywordAppsResponseFromJson(
  Map<String, dynamic> json,
) => _SearchKeywordAppsResponse(
  code: (json['code'] as num).toInt(),
  msg: json['msg'] as String?,
  data: json['data'] == null
      ? null
      : SearchKeywordAppsData.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$SearchKeywordAppsResponseToJson(
  _SearchKeywordAppsResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'msg': instance.msg,
  'data': instance.data,
};

_SearchKeywordAppsData _$SearchKeywordAppsDataFromJson(
  Map<String, dynamic> json,
) => _SearchKeywordAppsData(
  list:
      (json['list'] as List<dynamic>?)
          ?.map((e) => AppData.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
);

Map<String, dynamic> _$SearchKeywordAppsDataToJson(
  _SearchKeywordAppsData instance,
) => <String, dynamic>{'list': instance.list};

_AppData _$AppDataFromJson(Map<String, dynamic> json) => _AppData(
  appId: json['appId'] as String?,
  appName: json['appName'] as String?,
  subTitle: json['subTitle'] as String?,
  install: (json['install'] as num?)?.toInt(),
  installRate: (json['installRate'] as num?)?.toDouble(),
  category: json['category'] as String?,
  ratingCount: (json['ratingCount'] as num?)?.toInt(),
);

Map<String, dynamic> _$AppDataToJson(_AppData instance) => <String, dynamic>{
  'appId': instance.appId,
  'appName': instance.appName,
  'subTitle': instance.subTitle,
  'install': instance.install,
  'installRate': instance.installRate,
  'category': instance.category,
  'ratingCount': instance.ratingCount,
};
