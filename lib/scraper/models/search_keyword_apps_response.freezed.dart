// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_keyword_apps_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SearchKeywordAppsResponse {

 int get code; String? get msg; SearchKeywordAppsData? get data;
/// Create a copy of SearchKeywordAppsResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SearchKeywordAppsResponseCopyWith<SearchKeywordAppsResponse> get copyWith => _$SearchKeywordAppsResponseCopyWithImpl<SearchKeywordAppsResponse>(this as SearchKeywordAppsResponse, _$identity);

  /// Serializes this SearchKeywordAppsResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SearchKeywordAppsResponse&&(identical(other.code, code) || other.code == code)&&(identical(other.msg, msg) || other.msg == msg)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,msg,data);

@override
String toString() {
  return 'SearchKeywordAppsResponse(code: $code, msg: $msg, data: $data)';
}


}

/// @nodoc
abstract mixin class $SearchKeywordAppsResponseCopyWith<$Res>  {
  factory $SearchKeywordAppsResponseCopyWith(SearchKeywordAppsResponse value, $Res Function(SearchKeywordAppsResponse) _then) = _$SearchKeywordAppsResponseCopyWithImpl;
@useResult
$Res call({
 int code, String? msg, SearchKeywordAppsData? data
});


$SearchKeywordAppsDataCopyWith<$Res>? get data;

}
/// @nodoc
class _$SearchKeywordAppsResponseCopyWithImpl<$Res>
    implements $SearchKeywordAppsResponseCopyWith<$Res> {
  _$SearchKeywordAppsResponseCopyWithImpl(this._self, this._then);

  final SearchKeywordAppsResponse _self;
  final $Res Function(SearchKeywordAppsResponse) _then;

/// Create a copy of SearchKeywordAppsResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? code = null,Object? msg = freezed,Object? data = freezed,}) {
  return _then(_self.copyWith(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,msg: freezed == msg ? _self.msg : msg // ignore: cast_nullable_to_non_nullable
as String?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as SearchKeywordAppsData?,
  ));
}
/// Create a copy of SearchKeywordAppsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SearchKeywordAppsDataCopyWith<$Res>? get data {
    if (_self.data == null) {
    return null;
  }

  return $SearchKeywordAppsDataCopyWith<$Res>(_self.data!, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _SearchKeywordAppsResponse implements SearchKeywordAppsResponse {
  const _SearchKeywordAppsResponse({required this.code, this.msg, this.data});
  factory _SearchKeywordAppsResponse.fromJson(Map<String, dynamic> json) => _$SearchKeywordAppsResponseFromJson(json);

@override final  int code;
@override final  String? msg;
@override final  SearchKeywordAppsData? data;

/// Create a copy of SearchKeywordAppsResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SearchKeywordAppsResponseCopyWith<_SearchKeywordAppsResponse> get copyWith => __$SearchKeywordAppsResponseCopyWithImpl<_SearchKeywordAppsResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SearchKeywordAppsResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SearchKeywordAppsResponse&&(identical(other.code, code) || other.code == code)&&(identical(other.msg, msg) || other.msg == msg)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,msg,data);

@override
String toString() {
  return 'SearchKeywordAppsResponse(code: $code, msg: $msg, data: $data)';
}


}

/// @nodoc
abstract mixin class _$SearchKeywordAppsResponseCopyWith<$Res> implements $SearchKeywordAppsResponseCopyWith<$Res> {
  factory _$SearchKeywordAppsResponseCopyWith(_SearchKeywordAppsResponse value, $Res Function(_SearchKeywordAppsResponse) _then) = __$SearchKeywordAppsResponseCopyWithImpl;
@override @useResult
$Res call({
 int code, String? msg, SearchKeywordAppsData? data
});


@override $SearchKeywordAppsDataCopyWith<$Res>? get data;

}
/// @nodoc
class __$SearchKeywordAppsResponseCopyWithImpl<$Res>
    implements _$SearchKeywordAppsResponseCopyWith<$Res> {
  __$SearchKeywordAppsResponseCopyWithImpl(this._self, this._then);

  final _SearchKeywordAppsResponse _self;
  final $Res Function(_SearchKeywordAppsResponse) _then;

/// Create a copy of SearchKeywordAppsResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? msg = freezed,Object? data = freezed,}) {
  return _then(_SearchKeywordAppsResponse(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,msg: freezed == msg ? _self.msg : msg // ignore: cast_nullable_to_non_nullable
as String?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as SearchKeywordAppsData?,
  ));
}

/// Create a copy of SearchKeywordAppsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SearchKeywordAppsDataCopyWith<$Res>? get data {
    if (_self.data == null) {
    return null;
  }

  return $SearchKeywordAppsDataCopyWith<$Res>(_self.data!, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
mixin _$SearchKeywordAppsData {

 List<AppData> get list;
/// Create a copy of SearchKeywordAppsData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SearchKeywordAppsDataCopyWith<SearchKeywordAppsData> get copyWith => _$SearchKeywordAppsDataCopyWithImpl<SearchKeywordAppsData>(this as SearchKeywordAppsData, _$identity);

  /// Serializes this SearchKeywordAppsData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SearchKeywordAppsData&&const DeepCollectionEquality().equals(other.list, list));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(list));

@override
String toString() {
  return 'SearchKeywordAppsData(list: $list)';
}


}

/// @nodoc
abstract mixin class $SearchKeywordAppsDataCopyWith<$Res>  {
  factory $SearchKeywordAppsDataCopyWith(SearchKeywordAppsData value, $Res Function(SearchKeywordAppsData) _then) = _$SearchKeywordAppsDataCopyWithImpl;
@useResult
$Res call({
 List<AppData> list
});




}
/// @nodoc
class _$SearchKeywordAppsDataCopyWithImpl<$Res>
    implements $SearchKeywordAppsDataCopyWith<$Res> {
  _$SearchKeywordAppsDataCopyWithImpl(this._self, this._then);

  final SearchKeywordAppsData _self;
  final $Res Function(SearchKeywordAppsData) _then;

/// Create a copy of SearchKeywordAppsData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? list = null,}) {
  return _then(_self.copyWith(
list: null == list ? _self.list : list // ignore: cast_nullable_to_non_nullable
as List<AppData>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SearchKeywordAppsData implements SearchKeywordAppsData {
  const _SearchKeywordAppsData({final  List<AppData> list = const []}): _list = list;
  factory _SearchKeywordAppsData.fromJson(Map<String, dynamic> json) => _$SearchKeywordAppsDataFromJson(json);

 final  List<AppData> _list;
@override@JsonKey() List<AppData> get list {
  if (_list is EqualUnmodifiableListView) return _list;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_list);
}


/// Create a copy of SearchKeywordAppsData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SearchKeywordAppsDataCopyWith<_SearchKeywordAppsData> get copyWith => __$SearchKeywordAppsDataCopyWithImpl<_SearchKeywordAppsData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SearchKeywordAppsDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SearchKeywordAppsData&&const DeepCollectionEquality().equals(other._list, _list));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_list));

@override
String toString() {
  return 'SearchKeywordAppsData(list: $list)';
}


}

/// @nodoc
abstract mixin class _$SearchKeywordAppsDataCopyWith<$Res> implements $SearchKeywordAppsDataCopyWith<$Res> {
  factory _$SearchKeywordAppsDataCopyWith(_SearchKeywordAppsData value, $Res Function(_SearchKeywordAppsData) _then) = __$SearchKeywordAppsDataCopyWithImpl;
@override @useResult
$Res call({
 List<AppData> list
});




}
/// @nodoc
class __$SearchKeywordAppsDataCopyWithImpl<$Res>
    implements _$SearchKeywordAppsDataCopyWith<$Res> {
  __$SearchKeywordAppsDataCopyWithImpl(this._self, this._then);

  final _SearchKeywordAppsData _self;
  final $Res Function(_SearchKeywordAppsData) _then;

/// Create a copy of SearchKeywordAppsData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? list = null,}) {
  return _then(_SearchKeywordAppsData(
list: null == list ? _self._list : list // ignore: cast_nullable_to_non_nullable
as List<AppData>,
  ));
}


}


/// @nodoc
mixin _$AppData {

 String? get appId; String? get appName; String? get subTitle; int? get install; double? get installRate; String? get category; int? get ratingCount;
/// Create a copy of AppData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppDataCopyWith<AppData> get copyWith => _$AppDataCopyWithImpl<AppData>(this as AppData, _$identity);

  /// Serializes this AppData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppData&&(identical(other.appId, appId) || other.appId == appId)&&(identical(other.appName, appName) || other.appName == appName)&&(identical(other.subTitle, subTitle) || other.subTitle == subTitle)&&(identical(other.install, install) || other.install == install)&&(identical(other.installRate, installRate) || other.installRate == installRate)&&(identical(other.category, category) || other.category == category)&&(identical(other.ratingCount, ratingCount) || other.ratingCount == ratingCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,appId,appName,subTitle,install,installRate,category,ratingCount);

@override
String toString() {
  return 'AppData(appId: $appId, appName: $appName, subTitle: $subTitle, install: $install, installRate: $installRate, category: $category, ratingCount: $ratingCount)';
}


}

/// @nodoc
abstract mixin class $AppDataCopyWith<$Res>  {
  factory $AppDataCopyWith(AppData value, $Res Function(AppData) _then) = _$AppDataCopyWithImpl;
@useResult
$Res call({
 String? appId, String? appName, String? subTitle, int? install, double? installRate, String? category, int? ratingCount
});




}
/// @nodoc
class _$AppDataCopyWithImpl<$Res>
    implements $AppDataCopyWith<$Res> {
  _$AppDataCopyWithImpl(this._self, this._then);

  final AppData _self;
  final $Res Function(AppData) _then;

/// Create a copy of AppData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? appId = freezed,Object? appName = freezed,Object? subTitle = freezed,Object? install = freezed,Object? installRate = freezed,Object? category = freezed,Object? ratingCount = freezed,}) {
  return _then(_self.copyWith(
appId: freezed == appId ? _self.appId : appId // ignore: cast_nullable_to_non_nullable
as String?,appName: freezed == appName ? _self.appName : appName // ignore: cast_nullable_to_non_nullable
as String?,subTitle: freezed == subTitle ? _self.subTitle : subTitle // ignore: cast_nullable_to_non_nullable
as String?,install: freezed == install ? _self.install : install // ignore: cast_nullable_to_non_nullable
as int?,installRate: freezed == installRate ? _self.installRate : installRate // ignore: cast_nullable_to_non_nullable
as double?,category: freezed == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String?,ratingCount: freezed == ratingCount ? _self.ratingCount : ratingCount // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AppData implements AppData {
  const _AppData({this.appId, this.appName, this.subTitle, this.install, this.installRate, this.category, this.ratingCount});
  factory _AppData.fromJson(Map<String, dynamic> json) => _$AppDataFromJson(json);

@override final  String? appId;
@override final  String? appName;
@override final  String? subTitle;
@override final  int? install;
@override final  double? installRate;
@override final  String? category;
@override final  int? ratingCount;

/// Create a copy of AppData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AppDataCopyWith<_AppData> get copyWith => __$AppDataCopyWithImpl<_AppData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AppDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AppData&&(identical(other.appId, appId) || other.appId == appId)&&(identical(other.appName, appName) || other.appName == appName)&&(identical(other.subTitle, subTitle) || other.subTitle == subTitle)&&(identical(other.install, install) || other.install == install)&&(identical(other.installRate, installRate) || other.installRate == installRate)&&(identical(other.category, category) || other.category == category)&&(identical(other.ratingCount, ratingCount) || other.ratingCount == ratingCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,appId,appName,subTitle,install,installRate,category,ratingCount);

@override
String toString() {
  return 'AppData(appId: $appId, appName: $appName, subTitle: $subTitle, install: $install, installRate: $installRate, category: $category, ratingCount: $ratingCount)';
}


}

/// @nodoc
abstract mixin class _$AppDataCopyWith<$Res> implements $AppDataCopyWith<$Res> {
  factory _$AppDataCopyWith(_AppData value, $Res Function(_AppData) _then) = __$AppDataCopyWithImpl;
@override @useResult
$Res call({
 String? appId, String? appName, String? subTitle, int? install, double? installRate, String? category, int? ratingCount
});




}
/// @nodoc
class __$AppDataCopyWithImpl<$Res>
    implements _$AppDataCopyWith<$Res> {
  __$AppDataCopyWithImpl(this._self, this._then);

  final _AppData _self;
  final $Res Function(_AppData) _then;

/// Create a copy of AppData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? appId = freezed,Object? appName = freezed,Object? subTitle = freezed,Object? install = freezed,Object? installRate = freezed,Object? category = freezed,Object? ratingCount = freezed,}) {
  return _then(_AppData(
appId: freezed == appId ? _self.appId : appId // ignore: cast_nullable_to_non_nullable
as String?,appName: freezed == appName ? _self.appName : appName // ignore: cast_nullable_to_non_nullable
as String?,subTitle: freezed == subTitle ? _self.subTitle : subTitle // ignore: cast_nullable_to_non_nullable
as String?,install: freezed == install ? _self.install : install // ignore: cast_nullable_to_non_nullable
as int?,installRate: freezed == installRate ? _self.installRate : installRate // ignore: cast_nullable_to_non_nullable
as double?,category: freezed == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String?,ratingCount: freezed == ratingCount ? _self.ratingCount : ratingCount // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
