import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_keyword_response.freezed.dart';
part 'app_keyword_response.g.dart';

@freezed
sealed class AppKeywordResponse with _$AppKeywordResponse {
  const factory AppKeywordResponse({
    required int code,
    required String msg,
    required Data data,
  }) = _AppKeywordResponse;

  factory AppKeywordResponse.fromJson(Map<String, dynamic> json) =>
      _$AppKeywordResponseFromJson(json);
}

@freezed
sealed  class Data with _$Data {
  const factory Data({
    required int totalCount,
    required int pageSize,
    required int totalPage,
    required int currPage,
    required List<KeywordItem> list,
  }) = _Data;

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);
}

@freezed
sealed class KeywordItem with _$KeywordItem {
  const factory KeywordItem({
    required String keyword,
    bool? first,
    int? rank,
    int? relation,
    int? rankChange,
    int? hot,
    int? num,
    bool? tracked,
    String? trackedExportField,
    bool? hasWrong,
    String? wrong,
    bool? hasCorrective,
    List<String>? corrective,
    List<String>? metaInclude,
    int? ts,
    int? install,
    double? installRate,
    bool? coverage,
    int? changeNum,
    int? difficultyNum,
  }) = _KeywordItem;

  factory KeywordItem.fromJson(Map<String, dynamic> json) =>
      _$KeywordItemFromJson(json);
}