import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_n_app_response.freezed.dart';
part 'get_n_app_response.g.dart';

@freezed
sealed class GetNAppResponse with _$GetNAppResponse {
  const factory GetNAppResponse({
    required int code,
    required String msg,
    required Data data,
  }) = _GetNAppResponse;

  factory GetNAppResponse.fromJson(Map<String, dynamic> json) =>
      _$GetNAppResponseFromJson(json);
}

@freezed
sealed class Data with _$Data {
  const factory Data({
    required int totalCount,
    required int pageSize,
    required int totalPage,
    required int currPage,
    required List<App> list,
    required String keyword,
    required int resultCount,
    required int install,
    int? maxReach,
  }) = _Data;

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);
}

@freezed
sealed class App with _$App {
  const factory App({
    required int serialNumber,
    required bool display,
    required String appName,
    String? subTitle,
    required String sellerName,
    required String region,
    required String icon,
    required String appId,
    required String store,
    required int install,
    required double installRate,
  }) = _App;

  factory App.fromJson(Map<String, dynamic> json) => _$AppFromJson(json);
}