// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_keyword_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppKeywordResponse {

 int get code; String get msg; Data get data;
/// Create a copy of AppKeywordResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppKeywordResponseCopyWith<AppKeywordResponse> get copyWith => _$AppKeywordResponseCopyWithImpl<AppKeywordResponse>(this as AppKeywordResponse, _$identity);

  /// Serializes this AppKeywordResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppKeywordResponse&&(identical(other.code, code) || other.code == code)&&(identical(other.msg, msg) || other.msg == msg)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,msg,data);

@override
String toString() {
  return 'AppKeywordResponse(code: $code, msg: $msg, data: $data)';
}


}

/// @nodoc
abstract mixin class $AppKeywordResponseCopyWith<$Res>  {
  factory $AppKeywordResponseCopyWith(AppKeywordResponse value, $Res Function(AppKeywordResponse) _then) = _$AppKeywordResponseCopyWithImpl;
@useResult
$Res call({
 int code, String msg, Data data
});


$DataCopyWith<$Res> get data;

}
/// @nodoc
class _$AppKeywordResponseCopyWithImpl<$Res>
    implements $AppKeywordResponseCopyWith<$Res> {
  _$AppKeywordResponseCopyWithImpl(this._self, this._then);

  final AppKeywordResponse _self;
  final $Res Function(AppKeywordResponse) _then;

/// Create a copy of AppKeywordResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? code = null,Object? msg = null,Object? data = null,}) {
  return _then(_self.copyWith(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,msg: null == msg ? _self.msg : msg // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as Data,
  ));
}
/// Create a copy of AppKeywordResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DataCopyWith<$Res> get data {
  
  return $DataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _AppKeywordResponse implements AppKeywordResponse {
  const _AppKeywordResponse({required this.code, required this.msg, required this.data});
  factory _AppKeywordResponse.fromJson(Map<String, dynamic> json) => _$AppKeywordResponseFromJson(json);

@override final  int code;
@override final  String msg;
@override final  Data data;

/// Create a copy of AppKeywordResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AppKeywordResponseCopyWith<_AppKeywordResponse> get copyWith => __$AppKeywordResponseCopyWithImpl<_AppKeywordResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AppKeywordResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AppKeywordResponse&&(identical(other.code, code) || other.code == code)&&(identical(other.msg, msg) || other.msg == msg)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,msg,data);

@override
String toString() {
  return 'AppKeywordResponse(code: $code, msg: $msg, data: $data)';
}


}

/// @nodoc
abstract mixin class _$AppKeywordResponseCopyWith<$Res> implements $AppKeywordResponseCopyWith<$Res> {
  factory _$AppKeywordResponseCopyWith(_AppKeywordResponse value, $Res Function(_AppKeywordResponse) _then) = __$AppKeywordResponseCopyWithImpl;
@override @useResult
$Res call({
 int code, String msg, Data data
});


@override $DataCopyWith<$Res> get data;

}
/// @nodoc
class __$AppKeywordResponseCopyWithImpl<$Res>
    implements _$AppKeywordResponseCopyWith<$Res> {
  __$AppKeywordResponseCopyWithImpl(this._self, this._then);

  final _AppKeywordResponse _self;
  final $Res Function(_AppKeywordResponse) _then;

/// Create a copy of AppKeywordResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? msg = null,Object? data = null,}) {
  return _then(_AppKeywordResponse(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,msg: null == msg ? _self.msg : msg // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as Data,
  ));
}

/// Create a copy of AppKeywordResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DataCopyWith<$Res> get data {
  
  return $DataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
mixin _$Data {

 int get totalCount; int get pageSize; int get totalPage; int get currPage; List<KeywordItem> get list;
/// Create a copy of Data
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DataCopyWith<Data> get copyWith => _$DataCopyWithImpl<Data>(this as Data, _$identity);

  /// Serializes this Data to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Data&&(identical(other.totalCount, totalCount) || other.totalCount == totalCount)&&(identical(other.pageSize, pageSize) || other.pageSize == pageSize)&&(identical(other.totalPage, totalPage) || other.totalPage == totalPage)&&(identical(other.currPage, currPage) || other.currPage == currPage)&&const DeepCollectionEquality().equals(other.list, list));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalCount,pageSize,totalPage,currPage,const DeepCollectionEquality().hash(list));

@override
String toString() {
  return 'Data(totalCount: $totalCount, pageSize: $pageSize, totalPage: $totalPage, currPage: $currPage, list: $list)';
}


}

/// @nodoc
abstract mixin class $DataCopyWith<$Res>  {
  factory $DataCopyWith(Data value, $Res Function(Data) _then) = _$DataCopyWithImpl;
@useResult
$Res call({
 int totalCount, int pageSize, int totalPage, int currPage, List<KeywordItem> list
});




}
/// @nodoc
class _$DataCopyWithImpl<$Res>
    implements $DataCopyWith<$Res> {
  _$DataCopyWithImpl(this._self, this._then);

  final Data _self;
  final $Res Function(Data) _then;

/// Create a copy of Data
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalCount = null,Object? pageSize = null,Object? totalPage = null,Object? currPage = null,Object? list = null,}) {
  return _then(_self.copyWith(
totalCount: null == totalCount ? _self.totalCount : totalCount // ignore: cast_nullable_to_non_nullable
as int,pageSize: null == pageSize ? _self.pageSize : pageSize // ignore: cast_nullable_to_non_nullable
as int,totalPage: null == totalPage ? _self.totalPage : totalPage // ignore: cast_nullable_to_non_nullable
as int,currPage: null == currPage ? _self.currPage : currPage // ignore: cast_nullable_to_non_nullable
as int,list: null == list ? _self.list : list // ignore: cast_nullable_to_non_nullable
as List<KeywordItem>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Data implements Data {
  const _Data({required this.totalCount, required this.pageSize, required this.totalPage, required this.currPage, required final  List<KeywordItem> list}): _list = list;
  factory _Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

@override final  int totalCount;
@override final  int pageSize;
@override final  int totalPage;
@override final  int currPage;
 final  List<KeywordItem> _list;
@override List<KeywordItem> get list {
  if (_list is EqualUnmodifiableListView) return _list;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_list);
}


/// Create a copy of Data
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DataCopyWith<_Data> get copyWith => __$DataCopyWithImpl<_Data>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Data&&(identical(other.totalCount, totalCount) || other.totalCount == totalCount)&&(identical(other.pageSize, pageSize) || other.pageSize == pageSize)&&(identical(other.totalPage, totalPage) || other.totalPage == totalPage)&&(identical(other.currPage, currPage) || other.currPage == currPage)&&const DeepCollectionEquality().equals(other._list, _list));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalCount,pageSize,totalPage,currPage,const DeepCollectionEquality().hash(_list));

@override
String toString() {
  return 'Data(totalCount: $totalCount, pageSize: $pageSize, totalPage: $totalPage, currPage: $currPage, list: $list)';
}


}

/// @nodoc
abstract mixin class _$DataCopyWith<$Res> implements $DataCopyWith<$Res> {
  factory _$DataCopyWith(_Data value, $Res Function(_Data) _then) = __$DataCopyWithImpl;
@override @useResult
$Res call({
 int totalCount, int pageSize, int totalPage, int currPage, List<KeywordItem> list
});




}
/// @nodoc
class __$DataCopyWithImpl<$Res>
    implements _$DataCopyWith<$Res> {
  __$DataCopyWithImpl(this._self, this._then);

  final _Data _self;
  final $Res Function(_Data) _then;

/// Create a copy of Data
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalCount = null,Object? pageSize = null,Object? totalPage = null,Object? currPage = null,Object? list = null,}) {
  return _then(_Data(
totalCount: null == totalCount ? _self.totalCount : totalCount // ignore: cast_nullable_to_non_nullable
as int,pageSize: null == pageSize ? _self.pageSize : pageSize // ignore: cast_nullable_to_non_nullable
as int,totalPage: null == totalPage ? _self.totalPage : totalPage // ignore: cast_nullable_to_non_nullable
as int,currPage: null == currPage ? _self.currPage : currPage // ignore: cast_nullable_to_non_nullable
as int,list: null == list ? _self._list : list // ignore: cast_nullable_to_non_nullable
as List<KeywordItem>,
  ));
}


}


/// @nodoc
mixin _$KeywordItem {

 String get keyword; bool? get first; int? get rank; int? get relation; int? get rankChange; int? get hot; int? get num; bool? get tracked; String? get trackedExportField; bool? get hasWrong; String? get wrong; bool? get hasCorrective; List<String>? get corrective; List<String>? get metaInclude; int? get ts; int? get install; double? get installRate; bool? get coverage; int? get changeNum; int? get difficultyNum;
/// Create a copy of KeywordItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$KeywordItemCopyWith<KeywordItem> get copyWith => _$KeywordItemCopyWithImpl<KeywordItem>(this as KeywordItem, _$identity);

  /// Serializes this KeywordItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is KeywordItem&&(identical(other.keyword, keyword) || other.keyword == keyword)&&(identical(other.first, first) || other.first == first)&&(identical(other.rank, rank) || other.rank == rank)&&(identical(other.relation, relation) || other.relation == relation)&&(identical(other.rankChange, rankChange) || other.rankChange == rankChange)&&(identical(other.hot, hot) || other.hot == hot)&&(identical(other.num, num) || other.num == num)&&(identical(other.tracked, tracked) || other.tracked == tracked)&&(identical(other.trackedExportField, trackedExportField) || other.trackedExportField == trackedExportField)&&(identical(other.hasWrong, hasWrong) || other.hasWrong == hasWrong)&&(identical(other.wrong, wrong) || other.wrong == wrong)&&(identical(other.hasCorrective, hasCorrective) || other.hasCorrective == hasCorrective)&&const DeepCollectionEquality().equals(other.corrective, corrective)&&const DeepCollectionEquality().equals(other.metaInclude, metaInclude)&&(identical(other.ts, ts) || other.ts == ts)&&(identical(other.install, install) || other.install == install)&&(identical(other.installRate, installRate) || other.installRate == installRate)&&(identical(other.coverage, coverage) || other.coverage == coverage)&&(identical(other.changeNum, changeNum) || other.changeNum == changeNum)&&(identical(other.difficultyNum, difficultyNum) || other.difficultyNum == difficultyNum));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,keyword,first,rank,relation,rankChange,hot,num,tracked,trackedExportField,hasWrong,wrong,hasCorrective,const DeepCollectionEquality().hash(corrective),const DeepCollectionEquality().hash(metaInclude),ts,install,installRate,coverage,changeNum,difficultyNum]);

@override
String toString() {
  return 'KeywordItem(keyword: $keyword, first: $first, rank: $rank, relation: $relation, rankChange: $rankChange, hot: $hot, num: $num, tracked: $tracked, trackedExportField: $trackedExportField, hasWrong: $hasWrong, wrong: $wrong, hasCorrective: $hasCorrective, corrective: $corrective, metaInclude: $metaInclude, ts: $ts, install: $install, installRate: $installRate, coverage: $coverage, changeNum: $changeNum, difficultyNum: $difficultyNum)';
}


}

/// @nodoc
abstract mixin class $KeywordItemCopyWith<$Res>  {
  factory $KeywordItemCopyWith(KeywordItem value, $Res Function(KeywordItem) _then) = _$KeywordItemCopyWithImpl;
@useResult
$Res call({
 String keyword, bool? first, int? rank, int? relation, int? rankChange, int? hot, int? num, bool? tracked, String? trackedExportField, bool? hasWrong, String? wrong, bool? hasCorrective, List<String>? corrective, List<String>? metaInclude, int? ts, int? install, double? installRate, bool? coverage, int? changeNum, int? difficultyNum
});




}
/// @nodoc
class _$KeywordItemCopyWithImpl<$Res>
    implements $KeywordItemCopyWith<$Res> {
  _$KeywordItemCopyWithImpl(this._self, this._then);

  final KeywordItem _self;
  final $Res Function(KeywordItem) _then;

/// Create a copy of KeywordItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? keyword = null,Object? first = freezed,Object? rank = freezed,Object? relation = freezed,Object? rankChange = freezed,Object? hot = freezed,Object? num = freezed,Object? tracked = freezed,Object? trackedExportField = freezed,Object? hasWrong = freezed,Object? wrong = freezed,Object? hasCorrective = freezed,Object? corrective = freezed,Object? metaInclude = freezed,Object? ts = freezed,Object? install = freezed,Object? installRate = freezed,Object? coverage = freezed,Object? changeNum = freezed,Object? difficultyNum = freezed,}) {
  return _then(_self.copyWith(
keyword: null == keyword ? _self.keyword : keyword // ignore: cast_nullable_to_non_nullable
as String,first: freezed == first ? _self.first : first // ignore: cast_nullable_to_non_nullable
as bool?,rank: freezed == rank ? _self.rank : rank // ignore: cast_nullable_to_non_nullable
as int?,relation: freezed == relation ? _self.relation : relation // ignore: cast_nullable_to_non_nullable
as int?,rankChange: freezed == rankChange ? _self.rankChange : rankChange // ignore: cast_nullable_to_non_nullable
as int?,hot: freezed == hot ? _self.hot : hot // ignore: cast_nullable_to_non_nullable
as int?,num: freezed == num ? _self.num : num // ignore: cast_nullable_to_non_nullable
as int?,tracked: freezed == tracked ? _self.tracked : tracked // ignore: cast_nullable_to_non_nullable
as bool?,trackedExportField: freezed == trackedExportField ? _self.trackedExportField : trackedExportField // ignore: cast_nullable_to_non_nullable
as String?,hasWrong: freezed == hasWrong ? _self.hasWrong : hasWrong // ignore: cast_nullable_to_non_nullable
as bool?,wrong: freezed == wrong ? _self.wrong : wrong // ignore: cast_nullable_to_non_nullable
as String?,hasCorrective: freezed == hasCorrective ? _self.hasCorrective : hasCorrective // ignore: cast_nullable_to_non_nullable
as bool?,corrective: freezed == corrective ? _self.corrective : corrective // ignore: cast_nullable_to_non_nullable
as List<String>?,metaInclude: freezed == metaInclude ? _self.metaInclude : metaInclude // ignore: cast_nullable_to_non_nullable
as List<String>?,ts: freezed == ts ? _self.ts : ts // ignore: cast_nullable_to_non_nullable
as int?,install: freezed == install ? _self.install : install // ignore: cast_nullable_to_non_nullable
as int?,installRate: freezed == installRate ? _self.installRate : installRate // ignore: cast_nullable_to_non_nullable
as double?,coverage: freezed == coverage ? _self.coverage : coverage // ignore: cast_nullable_to_non_nullable
as bool?,changeNum: freezed == changeNum ? _self.changeNum : changeNum // ignore: cast_nullable_to_non_nullable
as int?,difficultyNum: freezed == difficultyNum ? _self.difficultyNum : difficultyNum // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _KeywordItem implements KeywordItem {
  const _KeywordItem({required this.keyword, this.first, this.rank, this.relation, this.rankChange, this.hot, this.num, this.tracked, this.trackedExportField, this.hasWrong, this.wrong, this.hasCorrective, final  List<String>? corrective, final  List<String>? metaInclude, this.ts, this.install, this.installRate, this.coverage, this.changeNum, this.difficultyNum}): _corrective = corrective,_metaInclude = metaInclude;
  factory _KeywordItem.fromJson(Map<String, dynamic> json) => _$KeywordItemFromJson(json);

@override final  String keyword;
@override final  bool? first;
@override final  int? rank;
@override final  int? relation;
@override final  int? rankChange;
@override final  int? hot;
@override final  int? num;
@override final  bool? tracked;
@override final  String? trackedExportField;
@override final  bool? hasWrong;
@override final  String? wrong;
@override final  bool? hasCorrective;
 final  List<String>? _corrective;
@override List<String>? get corrective {
  final value = _corrective;
  if (value == null) return null;
  if (_corrective is EqualUnmodifiableListView) return _corrective;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _metaInclude;
@override List<String>? get metaInclude {
  final value = _metaInclude;
  if (value == null) return null;
  if (_metaInclude is EqualUnmodifiableListView) return _metaInclude;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  int? ts;
@override final  int? install;
@override final  double? installRate;
@override final  bool? coverage;
@override final  int? changeNum;
@override final  int? difficultyNum;

/// Create a copy of KeywordItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$KeywordItemCopyWith<_KeywordItem> get copyWith => __$KeywordItemCopyWithImpl<_KeywordItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$KeywordItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _KeywordItem&&(identical(other.keyword, keyword) || other.keyword == keyword)&&(identical(other.first, first) || other.first == first)&&(identical(other.rank, rank) || other.rank == rank)&&(identical(other.relation, relation) || other.relation == relation)&&(identical(other.rankChange, rankChange) || other.rankChange == rankChange)&&(identical(other.hot, hot) || other.hot == hot)&&(identical(other.num, num) || other.num == num)&&(identical(other.tracked, tracked) || other.tracked == tracked)&&(identical(other.trackedExportField, trackedExportField) || other.trackedExportField == trackedExportField)&&(identical(other.hasWrong, hasWrong) || other.hasWrong == hasWrong)&&(identical(other.wrong, wrong) || other.wrong == wrong)&&(identical(other.hasCorrective, hasCorrective) || other.hasCorrective == hasCorrective)&&const DeepCollectionEquality().equals(other._corrective, _corrective)&&const DeepCollectionEquality().equals(other._metaInclude, _metaInclude)&&(identical(other.ts, ts) || other.ts == ts)&&(identical(other.install, install) || other.install == install)&&(identical(other.installRate, installRate) || other.installRate == installRate)&&(identical(other.coverage, coverage) || other.coverage == coverage)&&(identical(other.changeNum, changeNum) || other.changeNum == changeNum)&&(identical(other.difficultyNum, difficultyNum) || other.difficultyNum == difficultyNum));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,keyword,first,rank,relation,rankChange,hot,num,tracked,trackedExportField,hasWrong,wrong,hasCorrective,const DeepCollectionEquality().hash(_corrective),const DeepCollectionEquality().hash(_metaInclude),ts,install,installRate,coverage,changeNum,difficultyNum]);

@override
String toString() {
  return 'KeywordItem(keyword: $keyword, first: $first, rank: $rank, relation: $relation, rankChange: $rankChange, hot: $hot, num: $num, tracked: $tracked, trackedExportField: $trackedExportField, hasWrong: $hasWrong, wrong: $wrong, hasCorrective: $hasCorrective, corrective: $corrective, metaInclude: $metaInclude, ts: $ts, install: $install, installRate: $installRate, coverage: $coverage, changeNum: $changeNum, difficultyNum: $difficultyNum)';
}


}

/// @nodoc
abstract mixin class _$KeywordItemCopyWith<$Res> implements $KeywordItemCopyWith<$Res> {
  factory _$KeywordItemCopyWith(_KeywordItem value, $Res Function(_KeywordItem) _then) = __$KeywordItemCopyWithImpl;
@override @useResult
$Res call({
 String keyword, bool? first, int? rank, int? relation, int? rankChange, int? hot, int? num, bool? tracked, String? trackedExportField, bool? hasWrong, String? wrong, bool? hasCorrective, List<String>? corrective, List<String>? metaInclude, int? ts, int? install, double? installRate, bool? coverage, int? changeNum, int? difficultyNum
});




}
/// @nodoc
class __$KeywordItemCopyWithImpl<$Res>
    implements _$KeywordItemCopyWith<$Res> {
  __$KeywordItemCopyWithImpl(this._self, this._then);

  final _KeywordItem _self;
  final $Res Function(_KeywordItem) _then;

/// Create a copy of KeywordItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? keyword = null,Object? first = freezed,Object? rank = freezed,Object? relation = freezed,Object? rankChange = freezed,Object? hot = freezed,Object? num = freezed,Object? tracked = freezed,Object? trackedExportField = freezed,Object? hasWrong = freezed,Object? wrong = freezed,Object? hasCorrective = freezed,Object? corrective = freezed,Object? metaInclude = freezed,Object? ts = freezed,Object? install = freezed,Object? installRate = freezed,Object? coverage = freezed,Object? changeNum = freezed,Object? difficultyNum = freezed,}) {
  return _then(_KeywordItem(
keyword: null == keyword ? _self.keyword : keyword // ignore: cast_nullable_to_non_nullable
as String,first: freezed == first ? _self.first : first // ignore: cast_nullable_to_non_nullable
as bool?,rank: freezed == rank ? _self.rank : rank // ignore: cast_nullable_to_non_nullable
as int?,relation: freezed == relation ? _self.relation : relation // ignore: cast_nullable_to_non_nullable
as int?,rankChange: freezed == rankChange ? _self.rankChange : rankChange // ignore: cast_nullable_to_non_nullable
as int?,hot: freezed == hot ? _self.hot : hot // ignore: cast_nullable_to_non_nullable
as int?,num: freezed == num ? _self.num : num // ignore: cast_nullable_to_non_nullable
as int?,tracked: freezed == tracked ? _self.tracked : tracked // ignore: cast_nullable_to_non_nullable
as bool?,trackedExportField: freezed == trackedExportField ? _self.trackedExportField : trackedExportField // ignore: cast_nullable_to_non_nullable
as String?,hasWrong: freezed == hasWrong ? _self.hasWrong : hasWrong // ignore: cast_nullable_to_non_nullable
as bool?,wrong: freezed == wrong ? _self.wrong : wrong // ignore: cast_nullable_to_non_nullable
as String?,hasCorrective: freezed == hasCorrective ? _self.hasCorrective : hasCorrective // ignore: cast_nullable_to_non_nullable
as bool?,corrective: freezed == corrective ? _self._corrective : corrective // ignore: cast_nullable_to_non_nullable
as List<String>?,metaInclude: freezed == metaInclude ? _self._metaInclude : metaInclude // ignore: cast_nullable_to_non_nullable
as List<String>?,ts: freezed == ts ? _self.ts : ts // ignore: cast_nullable_to_non_nullable
as int?,install: freezed == install ? _self.install : install // ignore: cast_nullable_to_non_nullable
as int?,installRate: freezed == installRate ? _self.installRate : installRate // ignore: cast_nullable_to_non_nullable
as double?,coverage: freezed == coverage ? _self.coverage : coverage // ignore: cast_nullable_to_non_nullable
as bool?,changeNum: freezed == changeNum ? _self.changeNum : changeNum // ignore: cast_nullable_to_non_nullable
as int?,difficultyNum: freezed == difficultyNum ? _self.difficultyNum : difficultyNum // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
