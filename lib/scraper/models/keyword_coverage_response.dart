import 'package:freezed_annotation/freezed_annotation.dart';

part 'keyword_coverage_response.freezed.dart';
part 'keyword_coverage_response.g.dart';

@freezed
sealed class KeywordCoverageResponse with _$KeywordCoverageResponse {
  const factory KeywordCoverageResponse({
    required int code,
    String? msg,
    KeywordCoverageData? data,
  }) = _KeywordCoverageResponse;

  factory KeywordCoverageResponse.fromJson(Map<String, dynamic> json) =>
      _$KeywordCoverageResponseFromJson(json);
}

@freezed
sealed class KeywordCoverageData with _$KeywordCoverageData {
  const factory KeywordCoverageData({@Default([]) List<KeywordData> list}) =
      _KeywordCoverageData;

  factory KeywordCoverageData.fromJson(Map<String, dynamic> json) =>
      _$KeywordCoverageDataFromJson(json);
}

@freezed
sealed class KeywordData with _$KeywordData {
  const factory KeywordData({
    String? keyword,
    int? volume,
    int? difficulty,
    int? rank,
    int? install,
  }) = _KeywordData;

  factory KeywordData.fromJson(Map<String, dynamic> json) =>
      _$KeywordDataFromJson(json);
}
