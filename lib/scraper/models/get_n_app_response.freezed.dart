// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'get_n_app_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GetNAppResponse {

 int get code; String get msg; Data get data;
/// Create a copy of GetNAppResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GetNAppResponseCopyWith<GetNAppResponse> get copyWith => _$GetNAppResponseCopyWithImpl<GetNAppResponse>(this as GetNAppResponse, _$identity);

  /// Serializes this GetNAppResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GetNAppResponse&&(identical(other.code, code) || other.code == code)&&(identical(other.msg, msg) || other.msg == msg)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,msg,data);

@override
String toString() {
  return 'GetNAppResponse(code: $code, msg: $msg, data: $data)';
}


}

/// @nodoc
abstract mixin class $GetNAppResponseCopyWith<$Res>  {
  factory $GetNAppResponseCopyWith(GetNAppResponse value, $Res Function(GetNAppResponse) _then) = _$GetNAppResponseCopyWithImpl;
@useResult
$Res call({
 int code, String msg, Data data
});


$DataCopyWith<$Res> get data;

}
/// @nodoc
class _$GetNAppResponseCopyWithImpl<$Res>
    implements $GetNAppResponseCopyWith<$Res> {
  _$GetNAppResponseCopyWithImpl(this._self, this._then);

  final GetNAppResponse _self;
  final $Res Function(GetNAppResponse) _then;

/// Create a copy of GetNAppResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? code = null,Object? msg = null,Object? data = null,}) {
  return _then(_self.copyWith(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,msg: null == msg ? _self.msg : msg // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as Data,
  ));
}
/// Create a copy of GetNAppResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DataCopyWith<$Res> get data {
  
  return $DataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _GetNAppResponse implements GetNAppResponse {
  const _GetNAppResponse({required this.code, required this.msg, required this.data});
  factory _GetNAppResponse.fromJson(Map<String, dynamic> json) => _$GetNAppResponseFromJson(json);

@override final  int code;
@override final  String msg;
@override final  Data data;

/// Create a copy of GetNAppResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetNAppResponseCopyWith<_GetNAppResponse> get copyWith => __$GetNAppResponseCopyWithImpl<_GetNAppResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GetNAppResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetNAppResponse&&(identical(other.code, code) || other.code == code)&&(identical(other.msg, msg) || other.msg == msg)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,code,msg,data);

@override
String toString() {
  return 'GetNAppResponse(code: $code, msg: $msg, data: $data)';
}


}

/// @nodoc
abstract mixin class _$GetNAppResponseCopyWith<$Res> implements $GetNAppResponseCopyWith<$Res> {
  factory _$GetNAppResponseCopyWith(_GetNAppResponse value, $Res Function(_GetNAppResponse) _then) = __$GetNAppResponseCopyWithImpl;
@override @useResult
$Res call({
 int code, String msg, Data data
});


@override $DataCopyWith<$Res> get data;

}
/// @nodoc
class __$GetNAppResponseCopyWithImpl<$Res>
    implements _$GetNAppResponseCopyWith<$Res> {
  __$GetNAppResponseCopyWithImpl(this._self, this._then);

  final _GetNAppResponse _self;
  final $Res Function(_GetNAppResponse) _then;

/// Create a copy of GetNAppResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? msg = null,Object? data = null,}) {
  return _then(_GetNAppResponse(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,msg: null == msg ? _self.msg : msg // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as Data,
  ));
}

/// Create a copy of GetNAppResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DataCopyWith<$Res> get data {
  
  return $DataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
mixin _$Data {

 int get totalCount; int get pageSize; int get totalPage; int get currPage; List<App> get list; String get keyword; int get resultCount; int get install; int? get maxReach;
/// Create a copy of Data
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DataCopyWith<Data> get copyWith => _$DataCopyWithImpl<Data>(this as Data, _$identity);

  /// Serializes this Data to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Data&&(identical(other.totalCount, totalCount) || other.totalCount == totalCount)&&(identical(other.pageSize, pageSize) || other.pageSize == pageSize)&&(identical(other.totalPage, totalPage) || other.totalPage == totalPage)&&(identical(other.currPage, currPage) || other.currPage == currPage)&&const DeepCollectionEquality().equals(other.list, list)&&(identical(other.keyword, keyword) || other.keyword == keyword)&&(identical(other.resultCount, resultCount) || other.resultCount == resultCount)&&(identical(other.install, install) || other.install == install)&&(identical(other.maxReach, maxReach) || other.maxReach == maxReach));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalCount,pageSize,totalPage,currPage,const DeepCollectionEquality().hash(list),keyword,resultCount,install,maxReach);

@override
String toString() {
  return 'Data(totalCount: $totalCount, pageSize: $pageSize, totalPage: $totalPage, currPage: $currPage, list: $list, keyword: $keyword, resultCount: $resultCount, install: $install, maxReach: $maxReach)';
}


}

/// @nodoc
abstract mixin class $DataCopyWith<$Res>  {
  factory $DataCopyWith(Data value, $Res Function(Data) _then) = _$DataCopyWithImpl;
@useResult
$Res call({
 int totalCount, int pageSize, int totalPage, int currPage, List<App> list, String keyword, int resultCount, int install, int? maxReach
});




}
/// @nodoc
class _$DataCopyWithImpl<$Res>
    implements $DataCopyWith<$Res> {
  _$DataCopyWithImpl(this._self, this._then);

  final Data _self;
  final $Res Function(Data) _then;

/// Create a copy of Data
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalCount = null,Object? pageSize = null,Object? totalPage = null,Object? currPage = null,Object? list = null,Object? keyword = null,Object? resultCount = null,Object? install = null,Object? maxReach = freezed,}) {
  return _then(_self.copyWith(
totalCount: null == totalCount ? _self.totalCount : totalCount // ignore: cast_nullable_to_non_nullable
as int,pageSize: null == pageSize ? _self.pageSize : pageSize // ignore: cast_nullable_to_non_nullable
as int,totalPage: null == totalPage ? _self.totalPage : totalPage // ignore: cast_nullable_to_non_nullable
as int,currPage: null == currPage ? _self.currPage : currPage // ignore: cast_nullable_to_non_nullable
as int,list: null == list ? _self.list : list // ignore: cast_nullable_to_non_nullable
as List<App>,keyword: null == keyword ? _self.keyword : keyword // ignore: cast_nullable_to_non_nullable
as String,resultCount: null == resultCount ? _self.resultCount : resultCount // ignore: cast_nullable_to_non_nullable
as int,install: null == install ? _self.install : install // ignore: cast_nullable_to_non_nullable
as int,maxReach: freezed == maxReach ? _self.maxReach : maxReach // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Data implements Data {
  const _Data({required this.totalCount, required this.pageSize, required this.totalPage, required this.currPage, required final  List<App> list, required this.keyword, required this.resultCount, required this.install, this.maxReach}): _list = list;
  factory _Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

@override final  int totalCount;
@override final  int pageSize;
@override final  int totalPage;
@override final  int currPage;
 final  List<App> _list;
@override List<App> get list {
  if (_list is EqualUnmodifiableListView) return _list;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_list);
}

@override final  String keyword;
@override final  int resultCount;
@override final  int install;
@override final  int? maxReach;

/// Create a copy of Data
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DataCopyWith<_Data> get copyWith => __$DataCopyWithImpl<_Data>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Data&&(identical(other.totalCount, totalCount) || other.totalCount == totalCount)&&(identical(other.pageSize, pageSize) || other.pageSize == pageSize)&&(identical(other.totalPage, totalPage) || other.totalPage == totalPage)&&(identical(other.currPage, currPage) || other.currPage == currPage)&&const DeepCollectionEquality().equals(other._list, _list)&&(identical(other.keyword, keyword) || other.keyword == keyword)&&(identical(other.resultCount, resultCount) || other.resultCount == resultCount)&&(identical(other.install, install) || other.install == install)&&(identical(other.maxReach, maxReach) || other.maxReach == maxReach));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalCount,pageSize,totalPage,currPage,const DeepCollectionEquality().hash(_list),keyword,resultCount,install,maxReach);

@override
String toString() {
  return 'Data(totalCount: $totalCount, pageSize: $pageSize, totalPage: $totalPage, currPage: $currPage, list: $list, keyword: $keyword, resultCount: $resultCount, install: $install, maxReach: $maxReach)';
}


}

/// @nodoc
abstract mixin class _$DataCopyWith<$Res> implements $DataCopyWith<$Res> {
  factory _$DataCopyWith(_Data value, $Res Function(_Data) _then) = __$DataCopyWithImpl;
@override @useResult
$Res call({
 int totalCount, int pageSize, int totalPage, int currPage, List<App> list, String keyword, int resultCount, int install, int? maxReach
});




}
/// @nodoc
class __$DataCopyWithImpl<$Res>
    implements _$DataCopyWith<$Res> {
  __$DataCopyWithImpl(this._self, this._then);

  final _Data _self;
  final $Res Function(_Data) _then;

/// Create a copy of Data
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalCount = null,Object? pageSize = null,Object? totalPage = null,Object? currPage = null,Object? list = null,Object? keyword = null,Object? resultCount = null,Object? install = null,Object? maxReach = freezed,}) {
  return _then(_Data(
totalCount: null == totalCount ? _self.totalCount : totalCount // ignore: cast_nullable_to_non_nullable
as int,pageSize: null == pageSize ? _self.pageSize : pageSize // ignore: cast_nullable_to_non_nullable
as int,totalPage: null == totalPage ? _self.totalPage : totalPage // ignore: cast_nullable_to_non_nullable
as int,currPage: null == currPage ? _self.currPage : currPage // ignore: cast_nullable_to_non_nullable
as int,list: null == list ? _self._list : list // ignore: cast_nullable_to_non_nullable
as List<App>,keyword: null == keyword ? _self.keyword : keyword // ignore: cast_nullable_to_non_nullable
as String,resultCount: null == resultCount ? _self.resultCount : resultCount // ignore: cast_nullable_to_non_nullable
as int,install: null == install ? _self.install : install // ignore: cast_nullable_to_non_nullable
as int,maxReach: freezed == maxReach ? _self.maxReach : maxReach // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$App {

 int get serialNumber; bool get display; String get appName; String? get subTitle; String get sellerName; String get region; String get icon; String get appId; String get store; int get install; double get installRate;
/// Create a copy of App
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppCopyWith<App> get copyWith => _$AppCopyWithImpl<App>(this as App, _$identity);

  /// Serializes this App to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is App&&(identical(other.serialNumber, serialNumber) || other.serialNumber == serialNumber)&&(identical(other.display, display) || other.display == display)&&(identical(other.appName, appName) || other.appName == appName)&&(identical(other.subTitle, subTitle) || other.subTitle == subTitle)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName)&&(identical(other.region, region) || other.region == region)&&(identical(other.icon, icon) || other.icon == icon)&&(identical(other.appId, appId) || other.appId == appId)&&(identical(other.store, store) || other.store == store)&&(identical(other.install, install) || other.install == install)&&(identical(other.installRate, installRate) || other.installRate == installRate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,serialNumber,display,appName,subTitle,sellerName,region,icon,appId,store,install,installRate);

@override
String toString() {
  return 'App(serialNumber: $serialNumber, display: $display, appName: $appName, subTitle: $subTitle, sellerName: $sellerName, region: $region, icon: $icon, appId: $appId, store: $store, install: $install, installRate: $installRate)';
}


}

/// @nodoc
abstract mixin class $AppCopyWith<$Res>  {
  factory $AppCopyWith(App value, $Res Function(App) _then) = _$AppCopyWithImpl;
@useResult
$Res call({
 int serialNumber, bool display, String appName, String? subTitle, String sellerName, String region, String icon, String appId, String store, int install, double installRate
});




}
/// @nodoc
class _$AppCopyWithImpl<$Res>
    implements $AppCopyWith<$Res> {
  _$AppCopyWithImpl(this._self, this._then);

  final App _self;
  final $Res Function(App) _then;

/// Create a copy of App
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? serialNumber = null,Object? display = null,Object? appName = null,Object? subTitle = freezed,Object? sellerName = null,Object? region = null,Object? icon = null,Object? appId = null,Object? store = null,Object? install = null,Object? installRate = null,}) {
  return _then(_self.copyWith(
serialNumber: null == serialNumber ? _self.serialNumber : serialNumber // ignore: cast_nullable_to_non_nullable
as int,display: null == display ? _self.display : display // ignore: cast_nullable_to_non_nullable
as bool,appName: null == appName ? _self.appName : appName // ignore: cast_nullable_to_non_nullable
as String,subTitle: freezed == subTitle ? _self.subTitle : subTitle // ignore: cast_nullable_to_non_nullable
as String?,sellerName: null == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String,region: null == region ? _self.region : region // ignore: cast_nullable_to_non_nullable
as String,icon: null == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String,appId: null == appId ? _self.appId : appId // ignore: cast_nullable_to_non_nullable
as String,store: null == store ? _self.store : store // ignore: cast_nullable_to_non_nullable
as String,install: null == install ? _self.install : install // ignore: cast_nullable_to_non_nullable
as int,installRate: null == installRate ? _self.installRate : installRate // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _App implements App {
  const _App({required this.serialNumber, required this.display, required this.appName, this.subTitle, required this.sellerName, required this.region, required this.icon, required this.appId, required this.store, required this.install, required this.installRate});
  factory _App.fromJson(Map<String, dynamic> json) => _$AppFromJson(json);

@override final  int serialNumber;
@override final  bool display;
@override final  String appName;
@override final  String? subTitle;
@override final  String sellerName;
@override final  String region;
@override final  String icon;
@override final  String appId;
@override final  String store;
@override final  int install;
@override final  double installRate;

/// Create a copy of App
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AppCopyWith<_App> get copyWith => __$AppCopyWithImpl<_App>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AppToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _App&&(identical(other.serialNumber, serialNumber) || other.serialNumber == serialNumber)&&(identical(other.display, display) || other.display == display)&&(identical(other.appName, appName) || other.appName == appName)&&(identical(other.subTitle, subTitle) || other.subTitle == subTitle)&&(identical(other.sellerName, sellerName) || other.sellerName == sellerName)&&(identical(other.region, region) || other.region == region)&&(identical(other.icon, icon) || other.icon == icon)&&(identical(other.appId, appId) || other.appId == appId)&&(identical(other.store, store) || other.store == store)&&(identical(other.install, install) || other.install == install)&&(identical(other.installRate, installRate) || other.installRate == installRate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,serialNumber,display,appName,subTitle,sellerName,region,icon,appId,store,install,installRate);

@override
String toString() {
  return 'App(serialNumber: $serialNumber, display: $display, appName: $appName, subTitle: $subTitle, sellerName: $sellerName, region: $region, icon: $icon, appId: $appId, store: $store, install: $install, installRate: $installRate)';
}


}

/// @nodoc
abstract mixin class _$AppCopyWith<$Res> implements $AppCopyWith<$Res> {
  factory _$AppCopyWith(_App value, $Res Function(_App) _then) = __$AppCopyWithImpl;
@override @useResult
$Res call({
 int serialNumber, bool display, String appName, String? subTitle, String sellerName, String region, String icon, String appId, String store, int install, double installRate
});




}
/// @nodoc
class __$AppCopyWithImpl<$Res>
    implements _$AppCopyWith<$Res> {
  __$AppCopyWithImpl(this._self, this._then);

  final _App _self;
  final $Res Function(_App) _then;

/// Create a copy of App
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? serialNumber = null,Object? display = null,Object? appName = null,Object? subTitle = freezed,Object? sellerName = null,Object? region = null,Object? icon = null,Object? appId = null,Object? store = null,Object? install = null,Object? installRate = null,}) {
  return _then(_App(
serialNumber: null == serialNumber ? _self.serialNumber : serialNumber // ignore: cast_nullable_to_non_nullable
as int,display: null == display ? _self.display : display // ignore: cast_nullable_to_non_nullable
as bool,appName: null == appName ? _self.appName : appName // ignore: cast_nullable_to_non_nullable
as String,subTitle: freezed == subTitle ? _self.subTitle : subTitle // ignore: cast_nullable_to_non_nullable
as String?,sellerName: null == sellerName ? _self.sellerName : sellerName // ignore: cast_nullable_to_non_nullable
as String,region: null == region ? _self.region : region // ignore: cast_nullable_to_non_nullable
as String,icon: null == icon ? _self.icon : icon // ignore: cast_nullable_to_non_nullable
as String,appId: null == appId ? _self.appId : appId // ignore: cast_nullable_to_non_nullable
as String,store: null == store ? _self.store : store // ignore: cast_nullable_to_non_nullable
as String,install: null == install ? _self.install : install // ignore: cast_nullable_to_non_nullable
as int,installRate: null == installRate ? _self.installRate : installRate // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
