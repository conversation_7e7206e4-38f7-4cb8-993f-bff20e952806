// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_n_app_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GetNAppResponse _$GetNAppResponseFromJson(Map<String, dynamic> json) =>
    _GetNAppResponse(
      code: (json['code'] as num).toInt(),
      msg: json['msg'] as String,
      data: Data.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GetNAppResponseToJson(_GetNAppResponse instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': instance.data,
    };

_Data _$DataFromJson(Map<String, dynamic> json) => _Data(
  totalCount: (json['totalCount'] as num).toInt(),
  pageSize: (json['pageSize'] as num).toInt(),
  totalPage: (json['totalPage'] as num).toInt(),
  currPage: (json['currPage'] as num).toInt(),
  list: (json['list'] as List<dynamic>)
      .map((e) => App.fromJson(e as Map<String, dynamic>))
      .toList(),
  keyword: json['keyword'] as String,
  resultCount: (json['resultCount'] as num).toInt(),
  install: (json['install'] as num).toInt(),
  maxReach: (json['maxReach'] as num?)?.toInt(),
);

Map<String, dynamic> _$DataToJson(_Data instance) => <String, dynamic>{
  'totalCount': instance.totalCount,
  'pageSize': instance.pageSize,
  'totalPage': instance.totalPage,
  'currPage': instance.currPage,
  'list': instance.list,
  'keyword': instance.keyword,
  'resultCount': instance.resultCount,
  'install': instance.install,
  'maxReach': instance.maxReach,
};

_App _$AppFromJson(Map<String, dynamic> json) => _App(
  serialNumber: (json['serialNumber'] as num).toInt(),
  display: json['display'] as bool,
  appName: json['appName'] as String,
  subTitle: json['subTitle'] as String?,
  sellerName: json['sellerName'] as String,
  region: json['region'] as String,
  icon: json['icon'] as String,
  appId: json['appId'] as String,
  store: json['store'] as String,
  install: (json['install'] as num).toInt(),
  installRate: (json['installRate'] as num).toDouble(),
);

Map<String, dynamic> _$AppToJson(_App instance) => <String, dynamic>{
  'serialNumber': instance.serialNumber,
  'display': instance.display,
  'appName': instance.appName,
  'subTitle': instance.subTitle,
  'sellerName': instance.sellerName,
  'region': instance.region,
  'icon': instance.icon,
  'appId': instance.appId,
  'store': instance.store,
  'install': instance.install,
  'installRate': instance.installRate,
};
