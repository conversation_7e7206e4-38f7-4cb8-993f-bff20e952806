// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_keyword_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AppKeywordResponse _$AppKeywordResponseFromJson(Map<String, dynamic> json) =>
    _AppKeywordResponse(
      code: (json['code'] as num).toInt(),
      msg: json['msg'] as String,
      data: Data.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AppKeywordResponseToJson(_AppKeywordResponse instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': instance.data,
    };

_Data _$DataFromJson(Map<String, dynamic> json) => _Data(
  totalCount: (json['totalCount'] as num).toInt(),
  pageSize: (json['pageSize'] as num).toInt(),
  totalPage: (json['totalPage'] as num).toInt(),
  currPage: (json['currPage'] as num).toInt(),
  list: (json['list'] as List<dynamic>)
      .map((e) => KeywordItem.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$DataToJson(_Data instance) => <String, dynamic>{
  'totalCount': instance.totalCount,
  'pageSize': instance.pageSize,
  'totalPage': instance.totalPage,
  'currPage': instance.currPage,
  'list': instance.list,
};

_KeywordItem _$KeywordItemFromJson(Map<String, dynamic> json) => _KeywordItem(
  keyword: json['keyword'] as String,
  first: json['first'] as bool?,
  rank: (json['rank'] as num?)?.toInt(),
  relation: (json['relation'] as num?)?.toInt(),
  rankChange: (json['rankChange'] as num?)?.toInt(),
  hot: (json['hot'] as num?)?.toInt(),
  num: (json['num'] as num?)?.toInt(),
  tracked: json['tracked'] as bool?,
  trackedExportField: json['trackedExportField'] as String?,
  hasWrong: json['hasWrong'] as bool?,
  wrong: json['wrong'] as String?,
  hasCorrective: json['hasCorrective'] as bool?,
  corrective: (json['corrective'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  metaInclude: (json['metaInclude'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  ts: (json['ts'] as num?)?.toInt(),
  install: (json['install'] as num?)?.toInt(),
  installRate: (json['installRate'] as num?)?.toDouble(),
  coverage: json['coverage'] as bool?,
  changeNum: (json['changeNum'] as num?)?.toInt(),
  difficultyNum: (json['difficultyNum'] as num?)?.toInt(),
);

Map<String, dynamic> _$KeywordItemToJson(_KeywordItem instance) =>
    <String, dynamic>{
      'keyword': instance.keyword,
      'first': instance.first,
      'rank': instance.rank,
      'relation': instance.relation,
      'rankChange': instance.rankChange,
      'hot': instance.hot,
      'num': instance.num,
      'tracked': instance.tracked,
      'trackedExportField': instance.trackedExportField,
      'hasWrong': instance.hasWrong,
      'wrong': instance.wrong,
      'hasCorrective': instance.hasCorrective,
      'corrective': instance.corrective,
      'metaInclude': instance.metaInclude,
      'ts': instance.ts,
      'install': instance.install,
      'installRate': instance.installRate,
      'coverage': instance.coverage,
      'changeNum': instance.changeNum,
      'difficultyNum': instance.difficultyNum,
    };
