import 'package:freezed_annotation/freezed_annotation.dart';

part 'search_keyword_apps_response.freezed.dart';
part 'search_keyword_apps_response.g.dart';

@freezed
sealed class SearchKeywordAppsResponse with _$SearchKeywordAppsResponse {
  const factory SearchKeywordAppsResponse({
    required int code,
    String? msg,
    SearchKeywordAppsData? data,
  }) = _SearchKeywordAppsResponse;

  factory SearchKeywordAppsResponse.fromJson(Map<String, dynamic> json) =>
      _$SearchKeywordAppsResponseFromJson(json);
}

@freezed
sealed class SearchKeywordAppsData with _$SearchKeywordAppsData {
  const factory SearchKeywordAppsData({
    @Default([]) List<AppData> list,
  }) = _SearchKeywordAppsData;

  factory SearchKeywordAppsData.fromJson(Map<String, dynamic> json) =>
      _$SearchKeywordAppsDataFromJson(json);
}

@freezed
sealed class AppData with _$AppData {
  const factory AppData({
    String? appId,
    String? appName,
    String? subTitle,
    int? install,
    double? installRate,
    String? category,
    int? ratingCount,
  }) = _AppData;

  factory AppData.fromJson(Map<String, dynamic> json) =>
      _$AppDataFromJson(json);
}