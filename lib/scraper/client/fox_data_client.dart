import 'dart:convert';
import 'package:aso_scraper_web/scraper/client/endpoints.dart';
import 'package:aso_scraper_web/scraper/models/app_keyword_response.dart';
import 'package:aso_scraper_web/scraper/models/get_n_app_response.dart';
import 'package:aso_scraper_web/scraper/models/login_response.dart';
import 'package:dio/dio.dart';

class FoxDataASOClient {
  final String email;
  final String password;
  final String? apiKey;
  final Dio _dio;
 
  String? token;
  String? teamId;
  String? userId;

  FoxDataASOClient({required this.email, required this.password, this.apiKey})
    : _dio = Dio(
        BaseOptions(
          connectTimeout: Duration(seconds: 30),
          receiveTimeout: Duration(seconds: 30),
          baseUrl: 'https://platform.foxdata.com/',
        ),
      );

 Map<String, String> _getHeaders({
  required String country,
  String? keyword,
  String? appId,
}) {
  final encodedKeyword = keyword != null ? Uri.encodeComponent(keyword) : '';
  String referer = '';
  if (keyword != null) {
    referer = 'https://platform.foxdata.com/en/keyword-search/1/$country/as/$encodedKeyword';
  } else if (appId != null) {
    referer = 'https://platform.foxdata.com/en/ranked-keywords/$appId/$country/as';
  }

  return {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US,en;q=0.9,tr;q=0.8',
    'lang': 'en',
    'priority': 'u=1, i',
    'referer': referer, // Include referer
    'origin': 'https://platform.foxdata.com', // Add origin
    'content-type': 'application/json;charset=UTF-8', // Explicitly set
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'teamid': teamId ?? '',
    'token': token ?? '',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'userid': userId ?? '',
    'zonediff': '3',
  };
}

  // Login API Call
  Future<LoginResponse> login() async {
    final payload = {'email': email, 'password': password, 'appReportId': null};

    try {
      final response = await _dio.post(
        loginEndpoint,
        data: payload,
        options: Options(
          headers: {
            'accept': 'application/json, text/plain, */*',
            'content-type': 'application/json;charset=UTF-8',
          },
        ),
      );

      final loginResponse = LoginResponse.fromJson(response.data);
      if (loginResponse.code == 200 && loginResponse.data != null) {
        token = loginResponse.data!.token;
        userId = loginResponse.data!.id;

        // Extract team ID from JWT token
        try {
          final tokenParts = token!.split('.');
          if (tokenParts.length >= 2) {
            String payloadEncoded = tokenParts[1];
            // Add padding if needed
            payloadEncoded += '=' * (4 - payloadEncoded.length % 4);
            final payloadDecoded = base64Decode(payloadEncoded);
            final tokenData = jsonDecode(utf8.decode(payloadDecoded));
            final userInfo = jsonDecode(tokenData['userInfo'] ?? '{}');
            teamId = userInfo['teamId']?.toString() ?? '1920926207050674176';
          } else {
            teamId = '1920926207050674176'; // Fallback
          }
        } catch (e) {
          teamId = '1920926207050674176'; // Fallback
        }
        return loginResponse;
      } else {
        throw Exception(
          'Login failed: ${loginResponse.msg ?? 'Unknown error'}',
        );
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<GetNAppResponse> getTopNAppByKeyword({
    required String keyword,
    String country = 'US',
    int pageSize = 10,
  }) async {
    final currentTime = (DateTime.now().millisecondsSinceEpoch).toInt();
    final yesterday = currentTime - (24 * 60 * 60 * 1000);

    try {
      final response = await _dio.get(
        getTopNAppByKeywordEndpoint,
        options: Options(
          headers: _getHeaders(country: country, keyword: keyword),
        ),
        queryParameters: {
          'pageSize': pageSize,
          'currPage': 1,
          'region': country,
          'store': 'as',
          'key': keyword,
          'device': 'IPHONE',
          'os': 'IOS_14',
          'curDateSt': currentTime,
          'compareDateSt': yesterday,
          'tourId': '1924545133753004000',
          'includeMeta': false,
          'categoryId': 'ALL',
        },
      );
      final searchResponse = GetNAppResponse.fromJson(response.data);
      if (searchResponse.code == 200) {
        return searchResponse;
      } else {
        throw Exception('Failed to get top N apps for keyword \'$keyword\'');
      }
    } catch (e) {
      throw Exception('Failed to get top N apps for keyword \'$keyword\'');
    }
  }

  Future<AppKeywordResponse> getKeywordsByAppId({
    required String appId,
    String country = 'US',
    int pageSize = 10,
    int? minInstall,
    int? maxDifficulty,
    int? minRelation,
  }) async {
    final currentTime = (DateTime.now().millisecondsSinceEpoch ~/ 1000).toInt(); // Convert to seconds
final yesterday = currentTime - (24 * 60 * 60); // 24 hours in seconds

    try {
      final response = await _dio.post(
        keywordCoverageEndpoint,
        options: Options(
          headers: _getHeaders(country: country),
        ),
        data: {
  "page": {
    "currPage": 1,
    "pageSize": 50,
    "totalCount": null,
    "totalPage": null
  },
  "param": {
    "appId": appId,
    "device": "IPHONE",
    "region": country,
    "store": "AS",
    "sortField": "INSTALL", 
    "sortWay": "DESC",
    "startTime": currentTime,
    "endTime": yesterday,
    "keywords": [],
    "filterMarks": false,
    "trackStatus": "ALL",
    "quickFilterType": null,
    "isCoverage": null,
    "lastAddKeywords": [],
    "keywordType": [],
    "minHot": "", // Match Python default
    "maxHot": "",
    "minNum": "",
    "maxNum": "",
    "minRank": "",
    "maxRank": "",
    "minRelation": minRelation?.toString() ?? "",
    "maxRelation": "",
    "minInstall": minInstall?.toString() ?? "", 
    "maxInstall": "",
    "maxChangeScore": "",
    "minChangeScore": "",
    "maxDifficulty": maxDifficulty?.toString() ?? "", 
    "minDifficulty": "",
    "minInstallRate": "",
    "maxInstallRate": "",
    "mainAppId": appId
  }
}
      );
      final searchResponse = AppKeywordResponse.fromJson(response.data);
      if (searchResponse.code == 200) {
        return searchResponse;
      } else {
        throw Exception('Failed to get top N apps for keyword ');
      }
    } catch (e) {
      throw Exception('Failed to get top N apps for keyword ');
    }
  }
}


