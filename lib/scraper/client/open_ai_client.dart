import 'package:aso_scraper_web/scraper/config.dart';
import 'package:aso_scraper_web/scraper/models/app_keyword_response.dart';
import 'package:openai_dart/openai_dart.dart';

class OpenAI {
  final OpenAIClient openai;

  OpenAI() : openai = OpenAIClient(apiKey: openAIApiKey);

  Future<String> generateTitle({
    required List<KeywordItem> keywords,
    required String countryCode,
  }) async {
    final prompt =
        """
You are an App Store Optimization (ASO) expert tasked with creating an effective app title to maximize ranking in the $countryCode app store. Your goal is to craft a concise, keyword-optimized title that is highly relevant to the app's features, prioritizes high-impact keywords, and adheres to the 30-character limit.

**Instructions:**
- Use the provided app features and keywords to create a title that is clear, engaging, and optimized for search visibility.
- Prioritize keywords based on their order (earlier = more important), but balance this with difficulty and install count. Exclude keywords with low install counts or high difficulty unless they are highly relevant to the app.
- If keyword is a brand name or app name of another app, do not include it in the title.
- Ensure keywords are relevant to the app’s features and avoid typos or misspellings.
- Maximize the 30-character limit without sacrificing readability or natural language flow.
- Do not include branded terms unless explicitly mentioned in the app description.
- Output only the app title (no explanations or additional text).

**App Details:**
- **Country:** $countryCode
- **App Features:** $appDescription

**Keywords:**
${keywords.map((keyword) => "Keyword: '${keyword.keyword}', Difficulty: ${keyword.difficultyNum}, Installs: ${keyword.install}").join("\n")}

**Output Format:**
A single app title (max 30 characters). Do not include any additional text or explanations.
""";

    final response = await openai.createChatCompletion(
      request: CreateChatCompletionRequest(
        model: ChatCompletionModel.model(ChatCompletionModels.gpt4oMini),
        messages: [
          ChatCompletionMessage.user(
            content: ChatCompletionUserMessageContent.string(prompt),
          ),
        ],
      ),
    );
    return response.choices.first.message.content!;
  }

  Future<String> generateSubtitle({
    required List<KeywordItem> keywords,
    required String countryCode,
    required String appTitle,
  }) async {
    final prompt =
        """
You are an App Store Optimization (ASO) expert tasked with creating an effective app subtitle to enhance ranking and user engagement in the $countryCode app store. Your goal is to craft a concise, keyword-optimized subtitle and adheres to the 30-character limit.

**Instructions:**
- Use the provided app keywords to create a subtitle that is clear, engaging, and optimized for search visibility.
- Prioritize keywords based on their order (earlier = more important), but balance this with difficulty and install count. Exclude keywords with low install counts or high difficulty unless highly relevant to the app.
- Do not reuse keywords already included in the app title: '$appTitle'.
- If keyword is a brand name or app name of another app, do not include it in the subtitle.
- Ensure keywords are relevant to the app’s features and avoid typos or misspellings.
- Maximize the 30-character limit without sacrificing readability or natural language flow.
- Do not include branded terms unless explicitly mentioned in the app description.
- Output only the subtitle (no explanations or additional text).

**App Details:**
- **Country:** $countryCode
- **App Title:** $appTitle
- **App Features:** $appDescription

**Keywords:**
${keywords.map((keyword) => "Keyword: '${keyword.keyword}', Difficulty: ${keyword.difficultyNum}, Installs: ${keyword.install}").join("\n")}

**Output Format:**
A single app subtitle (max 30 characters). Do not include any additional text or explanations.
""";
    final response = await openai.createChatCompletion(
      request: CreateChatCompletionRequest(
        model: ChatCompletionModel.model(ChatCompletionModels.gpt4oMini),
        messages: [
          ChatCompletionMessage.user(
            content: ChatCompletionUserMessageContent.string(prompt),
          ),
        ],
      ),
    );
    return response.choices.first.message.content!;
  }

  Future<String> generateKeywords({
    required List<KeywordItem> keywords,
    required String countryCode,
    required String appTitle,
    required String appSubtitle,
  }) async {
    final prompt =
        """
You are an App Store Optimization (ASO) expert tasked with generating a keyword list for the keywords section to maximize search visibility in the $countryCode app store. Your goal is to create a concise, comma-separated list of keywords that complements the app title and subtitle, targets high-impact terms, and adheres to the 100-character limit.

**Instructions:**
- Use the provided app features and keywords to select terms that are highly relevant to the app and optimize search discoverability.
- Prioritize keywords based on their order (earlier = more important), but balance with difficulty and install count. Exclude keywords with low install counts or high difficulty unless highly relevant.
- Do not reuse keywords already included in the app title ('$appTitle') or subtitle ('$appSubtitle').
- Use singular or plural forms strategically to maximize coverage, avoiding redundant variations (e.g., use 'meditation' instead of both 'meditation' and 'meditations').
- Break down keyword phrases into individual, atomic terms (e.g., 'AI Tattoo Generator' becomes 'AI,Tattoo,Generator') to leverage Apple's algorithm for keyword matching.
- Separate keywords with commas and no spaces after commas (e.g., 'Keyword1,Keyword2') to maximize character usage.
- Maximize the 100-character limit without sacrificing relevance or clarity.
- Output only the keyword list (no explanations or additional text).

**App Details:**
- **Country:** $countryCode
- **App Title:** $appTitle
- **App Subtitle:** $appSubtitle
- **App Features:** $appDescription

**Keywords:**
${keywords.map((keyword) => "Keyword: '${keyword.keyword}', Difficulty: ${keyword.difficultyNum}, Installs: ${keyword.install}").join("\n")}

**Output Format:**
A comma-separated keyword list (max 100 characters). Do not include any additional text or explanations.
""";
    final response = await openai.createChatCompletion(
      request: CreateChatCompletionRequest(
        model: ChatCompletionModel.model(ChatCompletionModels.gpt4oMini),
        messages: [
          ChatCompletionMessage.user(
            content: ChatCompletionUserMessageContent.string(prompt),
          ),
        ],
      ),
    );
    return response.choices.first.message.content!;
  }

  Future<String> generatePromotionalText({
    required String countryCode,
    required String appTitle,
    required String appSubtitle,
  }) async {
    final prompt =
        """
You are an App Store Optimization (ASO) expert tasked with creating compelling promotional text to drive user engagement and downloads in the $countryCode app store. Your goal is to craft a concise, persuasive description that highlights the app’s unique features, appeals to the target audience, and adheres to the 170-character limit.

**Instructions:**
- Use the provided app features to create promotional text that is engaging, clear, and encourages downloads.
- Focus on unique selling points and benefits not fully covered in the app title ('$appTitle') or subtitle ('$appSubtitle') to ensure alignment and avoid redundancy.
- Avoid reusing specific words or phrases from the title or subtitle to maximize differentiation.
- Avoid branded terms unless explicitly mentioned in the app description.
- Use action-oriented language to create urgency or excitement (e.g., "Discover," "Transform," "Join").
- Avoid typos, misspellings, or promotional clichés that may reduce credibility.
- Output only the promotional text (no explanations or additional text).

**App Details:**
- **Country:** $countryCode
- **App Title:** $appTitle
- **App Subtitle:** $appSubtitle
- **App Features:** $appDescription

**Output Format:**
Promotional text (max 170 characters). Do not include any additional text or explanations.
""";
    final response = await openai.createChatCompletion(
      request: CreateChatCompletionRequest(
        model: ChatCompletionModel.model(ChatCompletionModels.gpt4oMini),
        messages: [
          ChatCompletionMessage.user(
            content: ChatCompletionUserMessageContent.string(prompt),
          ),
        ],
      ),
    );
    return response.choices.first.message.content!;
  }

  Future<String> generateAppDescription({
    required String countryCode,
    required String appTitle,
    required String appSubtitle,
  }) async {
    final prompt =
        """
You are an App Store Optimization (ASO) expert tasked with creating a compelling app description to engage users and drive downloads in the $countryCode app store. Your goal is to craft a clear, persuasive, and concise description that showcases the app’s unique features, benefits, and value proposition while adhering to the 4000-character limit.

**Instructions:**
- Use the provided app features to create a description that is engaging, user-focused, and encourages downloads.
- Highlight unique selling points and benefits not fully emphasized in the app title ('$appTitle') or subtitle ('$appSubtitle') to ensure alignment and avoid redundancy.
- Avoid reusing specific words or phrases from the title or subtitle as primary keywords to maximize differentiation, but maintain thematic consistency.
- Avoid branded terms unless explicitly mentioned in the app description.
- Structure the description with:
  - An engaging opening (1–2 sentences) to hook users with the app’s core value.
  - A features section (bullet points or short paragraphs) detailing key functionalities and benefits.
  - A closing call-to-action to prompt downloads (e.g., "Download now," "Start today").
   - A mandatory 'Subscription Details' section at the end, in the language specified by $countryCode, exactly as follows:
    Subscription Details
    Payment will be charged to your iTunes Account at confirmation of purchase. Subscriptions renew automatically unless canceled at least 24 hours before the end of the current period. Manage auto-renewal in your App Store settings.
    Privacy Policy: $appPrivacyPolicyUrl
    Terms of Service: $appTermsOfServiceUrl
- Use action-oriented, conversational language to appeal to the target audience.
- Keep the tone punchy, readable, and professional, aiming for 200–500 words for optimal readability.
- Avoid typos, misspellings, or generic clichés that may reduce credibility.
- Output only the app description (no explanations or additional text).

**App Details:**
- **Country:** $countryCode
- **App Title:** $appTitle
- **App Subtitle:** $appSubtitle
- **App Features:** $appDescription

**Output Format:**
App description (max 4000 characters, aim for 200–500 words).
""";
    final response = await openai.createChatCompletion(
      request: CreateChatCompletionRequest(
        model: ChatCompletionModel.model(ChatCompletionModels.gpt4oMini),
        messages: [
          ChatCompletionMessage.user(
            content: ChatCompletionUserMessageContent.string(prompt),
          ),
        ],
      ),
    );
    return response.choices.first.message.content!;
  }
}
