import 'package:shared_preferences/shared_preferences.dart';
import 'package:aso_scraper_web/scraper/client/fox_data_client.dart';

class AuthService {
  static const String _foxDataEmailKey = 'fox_data_email';
  static const String _foxDataPasswordKey = 'fox_data_password';
  static const String _openAIApiKeyKey = 'openai_api_key';

  /// Check if user is authenticated by verifying stored credentials
  Future<bool> isAuthenticated() async {
    final prefs = await SharedPreferences.getInstance();
    final email = prefs.getString(_foxDataEmailKey);
    final password = prefs.getString(_foxDataPasswordKey);
    final apiKey = prefs.getString(_openAIApiKeyKey);

    return email != null && 
           email.isNotEmpty && 
           password != null && 
           password.isNotEmpty && 
           apiKey != null && 
           apiKey.isNotEmpty;
  }

  /// Validate credentials by attempting to login with Fox Data
  Future<bool> validateCredentials({
    required String email,
    required String password,
    required String openAIApiKey,
  }) async {
    try {
      // Validate Fox Data credentials
      final foxDataClient = FoxDataASOClient(
        email: email,
        password: password,
      );
      
      await foxDataClient.login();

      // Validate OpenAI API key by creating a client
      // Note: We're not making an actual API call here to avoid costs
      // but we can validate the format
      if (openAIApiKey.isEmpty || !openAIApiKey.startsWith('sk-')) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Save credentials to SharedPreferences
  Future<void> saveCredentials({
    required String email,
    required String password,
    required String openAIApiKey,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_foxDataEmailKey, email);
    await prefs.setString(_foxDataPasswordKey, password);
    await prefs.setString(_openAIApiKeyKey, openAIApiKey);
  }

  /// Get stored credentials
  Future<Map<String, String?>> getStoredCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'email': prefs.getString(_foxDataEmailKey),
      'password': prefs.getString(_foxDataPasswordKey),
      'openAIApiKey': prefs.getString(_openAIApiKeyKey),
    };
  }

  /// Clear stored credentials (logout)
  Future<void> clearCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_foxDataEmailKey);
    await prefs.remove(_foxDataPasswordKey);
    await prefs.remove(_openAIApiKeyKey);
  }
}
